# LevelPageController 配置说明

## 当前问题
- ✅ 成功找到 GlobalManagerController
- ✅ 成功设置当前关卡
- ❌ 地图节点没有显示（可能是节点属性未配置）

## 需要在编辑器中配置的节点属性

### 必需配置
在 Cocos Creator 编辑器中，找到挂载了 `LevelPageController` 组件的节点，然后配置以下属性：

#### 基础属性
- `startGameButton` - 开始游戏按钮
- `mineCountLabel` - 地雷数显示标签
- `levelPageNode` - level_page 节点
- `gameMap1Node` - game_map_1 节点
- `gameMap2Node` - game_map_2 节点

#### 方形地图节点（game_map_1 下的节点）
- `qipan8x8Node` - 路径：`level_page/game_map_1/chess_bg/qipan8*8`
- `qipan8x9Node` - 路径：`level_page/game_map_1/chess_bg/qipan8*9`
- `qipan9x9Node` - 路径：`level_page/game_map_1/chess_bg/qipan9*9`
- `qipan9x10Node` - 路径：`level_page/game_map_1/chess_bg/qipan9*10`
- `qipan10x10Node` - 路径：`level_page/game_map_1/chess_bg/qipan10*10`

#### 特殊关卡节点（game_map_2 下的节点）
- `levelS001Node` - 路径：`level_page/game_map_2/game_bg/Level_S001`
- `levelS002Node` - 路径：`level_page/game_map_2/game_bg/Level_S002`
- `levelS003Node` - 路径：`level_page/game_map_2/game_bg/Level_S003`
- `levelS004Node` - 路径：`level_page/game_map_2/game_bg/Level_S004`
- `levelS005Node` - 路径：`level_page/game_map_2/game_bg/Level_S005`
- `levelS006Node` - 路径：`level_page/game_map_2/game_bg/Level_S006`

## 配置步骤

### 1. 找到 LevelPageController 组件
1. 在场景中找到关卡页面节点（可能是 `level_page` 或类似名称）
2. 确认该节点上挂载了 `LevelPageController` 组件

### 2. 配置节点引用
1. 选中挂载了 `LevelPageController` 的节点
2. 在属性检查器中找到 `LevelPageController` 组件
3. 将对应的节点拖拽到相应的属性字段中

### 3. 验证配置
运行游戏并点击"开始游戏"按钮，查看控制台输出：

**正确配置的输出**：
```
地图节点配置状态：
qipan8x8Node: ✅
qipan8x9Node: ✅
qipan9x9Node: ✅
...
✅ 显示地图节点: qipan8*8
```

**未配置的输出**：
```
地图节点配置状态：
qipan8x8Node: ❌
qipan8x9Node: ❌
...
❌ 地图节点未找到: qipan8*8
请在编辑器中为 LevelPageController 配置 qipan8*8 节点属性
```

## 关卡与地图节点对应关系

- **第1关**：应该显示 `qipan8*8` 节点
- **第5关**：应该显示 `Level_S001` 节点
- **第6关**：应该显示 `qipan8*9` 节点
- 以此类推...

## 故障排除

### 问题1：找不到 LevelPageController 组件
- 确认关卡页面节点上已挂载 `LevelPageController` 组件
- 确认 `GlobalManagerController` 的 `levelPage` 属性指向正确的节点

### 问题2：节点路径不匹配
- 检查场景中的实际节点结构
- 确认节点名称与代码中的路径一致

### 问题3：节点属性为空
- 在编辑器中重新配置节点引用
- 确保拖拽的是正确的节点

## 测试步骤

1. 配置完所有节点属性后
2. 点击"开始游戏"按钮
3. 查看控制台输出，确认：
   - 所有需要的节点都显示 ✅
   - 看到 `✅ 显示地图节点: xxx` 的消息
   - 对应的地图节点在场景中变为可见状态
