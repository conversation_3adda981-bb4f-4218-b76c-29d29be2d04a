export const language = {
//这部分是通用的
    kickout1 : 'You have been asked to leave the room',//您被请出房间
    LeaveRoom : 'The room is dissolved',//房间已解散
    InsufficientBalance : 'The current balance is insufficient, please go to purchase',//余额不足
    GameRouteNotFound : 'Game route not found',//游戏线路异常
    NetworkError : 'network error',//网络异常
    RoomIsFull : 'Room is full',//房间已满
    EnterRoomNumber : 'Enter room number',//输入房间号
    GetUserInfoFailed : 'get user info failed',//获取用户信息失败
    RoomDoesNotExist : 'Room does not exist',//房间不存在
    FailedToDeductGoldCoins : 'Failed to deduct gold coins',//扣除金币失败
    ExitApplication : 'Are you sure to leave?',//完全退出游戏
    QuitTheGame : 'Once you exit the game, you won’t be able to return to it.',//退出本局游戏

    NotEnoughPlayers : 'Not enough players',//玩家数量不足
    TheGameIsFullOfPlayers : 'The game is full of players',//玩家数量已满
    kickout2: 'Whether to kick {0} out of the room?',//踢出玩家文案

    upSeat:'Join', //上座
    downSeat:'Leave', //下座
    startGame:'Start', //开始游戏
    readyGame:'Ready', //准备
    cancelGame:'Cancel', //取消准备
    cancel:'Cancel', 
    confirm:'Confirm', 
    kickout3:'Kick Out', 
    back:'Back',//返回
    leave:'Leave', //退出
    music:'Music',  //音乐
    sound:'Sound', //音效
    join:'Join', //加入
    create:'Create', //创建
    auto:'Auto',
    Room:'Room',
    room_number:'Room Number', //房间号
    copy:'Copy', //复制
    game_amount:'Game Amount', //游戏费用
    player_numbers:'Player Numbers:', //玩家数量
    room_exist:'Room doesn’t exist',//房间不存在
    enter_room_number:'Enter room number',//输入房间号
    free:'Free',
    players:'Players', //玩家
    Player:'Player',
    Tickets:'Tickets',
    Empty:'Empty',

    nextlevel:'Next',//下一关

    relevel:'Play Again', //再来一局

    //帮助
    Tips : `Tips`,
    Tips1 : `1. Game Duration per Round: 120 seconds `,
    Tips2 : `2. Ranking Rules: Upon the conclusion of the game time, players are ranked based on their scores. Players with the same score will share the same ranking. `,
    Tips3 : `3. Elimination Rule: By swapping adjacent food items in the game, players can align identical food items. If 3 or more identical food items are adjacent after the swap, the elimination is successful, and points are awarded. `,
    Tips4 : `4. Item Elimination: Completing special eliminations will generate elimination items. These items will be used and consumed when swapped with adjacent blocks. Details are as follows:`,
    Tips5 : `5.Combo Elimination: When a player's elimination action triggers the generation of new food items, and the subsequent falling of these items leads to further eliminations, this is considered a combo elimination. During combo eliminations, players cannot perform any actions. Each sequence of eliminations caused by a single round of falling food items is counted as 1 round.`,
    
    //产生方式
    Generation_Method : `Generation Method & Effect`,
    Generation_Method1 : `1. Eliminate an entire row or column of food items.`,
    Generation_Method2 : `2. Eliminate 12 food items extending outward from its center.`,
    Generation_Method3 : `3. Eliminate all food items on the screen that are the same as the one swapped with the item. `,
    Generation_Method4 : `4. Eliminate one row and one column of food items, including chains and ice blocks.  `,
    Generation_Method5 : `5. Eliminate three rows and three columns of food items, including chains and ice blocks. `,
    Generation_Method6 : `6. Transform a random type of food item on the screen into a rocket and activate it.  `,
    Generation_Method7 : `7. Eliminate 24 food items extending outward from its center, including chains and ice blocks. `,
    Generation_Method8 : `8. Transform a random food block on the screen into a bomb and activate it.  `,
    Generation_Method9 : `9. Eliminate all food items on the screen, including chains and ice blocks.`,

    //常驻任务
    Permanent_Task : `Permanent Task`,
    Permanent_Task1 : `Every time a player eliminates 6 crabs, they will receive a reward of randomly eliminating a 2x2 area twice. If the current crab elimination satisfies the condition twice or more, only one 2x2 elimination will be granted starting from the second time.`,

    //随机任务
    Random_Task : `Random Task`,
    Random_Task1 : `The required elimination count scales with player count: +1 elimination per additional player (e.g. 2 players require 7 eliminations, 3 players require 8).`,
    Random_Task2 : `- Maximum 1 active mission at a time`,
    Random_Task3 : `- Mission sequence is randomly generated at match start and executed in sequence`,
    Random_Task4 : `- Eliminate 7xdesignated colors → Spawn 1 random special candy`,
    Random_Task5 : `- Eliminate 7xdesignated colors → Spawn 1 ice block`,
    Random_Task6 : `- Eliminate 7xdesignated colors → Spawn 1 chain lock`,
    Random_Task7 : `- Eliminate 7xdesignated colors → Spawn 1 random-shaped rocket item`,

    //锁链简介
    Chains : `Chains`,
    Chains1 : `1. Elimination Task: After the game starts, each player will receive a task assigned by the system. Completing the task will release a hindrance item—**chains**—to other players.  `,
    Chains2 : `2. Hindrance Effect of Chains: Randomly locks a food item in a specific position. During the restriction period, the locked position cannot be moved. The food item within the chain may change due to eliminations in other areas, meaning it does not lock a fixed food item.  `,
    Chains3 : `3. Chain Removal: Chains can be removed by performing a normal elimination in an adjacent position (blocks up, down, left, or right within 1 grid) or within the effective area of an item. One elimination is sufficient to remove the chain.  `,

    //冰块简介
    Ice_Blocks : `Ice Blocks:`,
    Ice_Blocks1 : `1. Elimination Task: After the game starts, each player will receive a task assigned by the system. Completing the task will release a hindrance item—**ice blocks**—to other players.  `,
    Ice_Blocks2 : `2. Hindrance Effect of Ice Blocks: Randomly freezes a food item. During the freezing period, the position can be passively moved, but players cannot actively move it.  `,
    Ice_Blocks3 : `3. Ice Block Removal: Ice blocks can be removed by performing a normal elimination in an adjacent position (blocks up, down, left, or right within 1 grid) or within the effective area of an item. One elimination is sufficient to remove the ice block.`,

    //得分细则
    Scoring_Details : `Scoring Details`,
    Scoring_Details1 : `Normal Elimination: Score per block eliminated : 20 points;`,
    Scoring_Details2: `Consecutive Elimination: Score per block eliminated : Number of consecutive eliminations x 20 points;`,
    Scoring_Details3 : `Rocket Elimination: Rocket activation score : 300 points; Score per block eliminated by rocket : 30 points;`,
    Scoring_Details4 : `Bomb Elimination: Bomb activation score : 500 points; Score per block eliminated by bomb : 50 points;`,
    Scoring_Details5 : `Rainbow Elimination: Rainbow activation score : 600 points; Score per block eliminated by rainbow : 60 points;`,
    Scoring_Details6 : `Combined Rocket Elimination: Dual rocket activation score : 1200 points; Score per block eliminated by super rocket : 60 points;`,
    Scoring_Details7 : `Bomb-Rocket Combined Elimination: Bomb-rocket combination activation score : 1600 points; Score per block eliminated by bomb-rocket : 80 points;`,
    Scoring_Details8 : `Rainbow-Rocket Combined Elimination: Rainbow-rocket combination activation score : 1800 points; Score per block eliminated by rainbow-rocket : 90 points;`,
    Scoring_Details9 : `Combined Bomb Elimination: Super bomb activation score : 2000 points; Score per block eliminated by super bomb : 100 points;`,
    Scoring_Details10 : `Rainbow Bomb Elimination: Rainbow bomb activation score : 2200 points; Score per block eliminated by rainbow bomb : 110 points;`,
    Scoring_Details11 : `Combined Rainbow Elimination: Super rainbow activation score : 2400 points; Score per block eliminated by super rainbow : 120 points;`,
};

const cocos = cc as any;
if (!cocos.Jou_i18n) cocos.Jou_i18n = {};
cocos.Jou_i18n.en = language;