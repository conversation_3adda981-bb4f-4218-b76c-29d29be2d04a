export const language = {
//这部分是通用的
    kickout1: '您被请出房间',
    LeaveRoom: '房间已解散',
    InsufficientBalance: '余额不足，去充值',
    GameRouteNotFound: '游戏线路异常',
    NetworkError: '网络异常',
    RoomIsFull: '房间已满',
    EnterRoomNumber: '输入房间号',
    GetUserInfoFailed: '获取用户信息失败',
    RoomDoesNotExist: '房间不存在',
    FailedToDeductGoldCoins: '扣除金币失败',
    ExitApplication: '确定退出游戏？',
    QuitTheGame: '退出后将无法返回游戏。',

    NotEnoughPlayers: '玩家数量不足',
    TheGameIsFullOfPlayers: '玩家数量已满',
    kickout2: '是否将 {0} 请出房间?',//踢出玩家文案

    upSeat:'加入游戏',
    downSeat:'退出游戏', 
    startGame:'开始', 
    readyGame:'准备', 
    cancelGame:'取消准备', 
    cancel:'取消', 
    confirm:'确定', 
    kickout3:'踢出', 
    back:'返回',//返回
    leave:'退出',
    music:'音乐',
    sound:'音效',
    join:'加入', //加入
    create:'创建', //创建
    auto:'匹配',
    Room:'房间',
    room_number:'房间号', //房间号
    copy:'复制', //复制
    game_amount:'游戏费用', //游戏费用
    player_numbers:'玩家数量:', //玩家数量
    room_exist:'房间不存在',//房间不存在
    enter_room_number:'输入房间号',//输入房间号
    free:'免费',
    players:'玩家', //玩家
    Player:'玩家',
    Tickets:'门票',
    Empty:'空位',

    nextlevel:'下一关',//下一关

    relevel:'再玩一次', //再来一局

    //帮助
    Tips: `帮助`,
    Tips1: `1. 每局游戏比赛时间：120秒。`,
    Tips2: `2. 排名规则：游戏时间结束后，根据玩家的得分高低进行排名，得分相同排名相同。`,
    Tips3: `3. 消除规则：通过移动游戏中的食物与临近的食物交换位置使相同的食物处于相邻状态，若满足相邻的同样食物>=3个位置交换成功，则完成消除得分。`,
    Tips4: `4. 道具消除：完成特殊的消除会生成消除道具，道具和附近块交换位置后会使用并消耗。`,
    Tips5: `5. 连续消除：玩家一次消除行为引发新食物产生，食物下落后触发的消除为连续消除，连续消除期间不可操作。一次下落期间发生的连续消除记为1轮。`,

    //产生方式
    Generation_Method: `产生方式和使用效果`,
    Generation_Method1: `1. 消除整列或整行食物。`,
    Generation_Method2: `2. 消除其中心向外延伸的12个食物。`,
    Generation_Method3: `3. 消除屏幕中所有与该道具交换位置相同的食物。`,
    Generation_Method4: `4. 消除1行+1列的食物，包括锁链和冰块。`,
    Generation_Method5: `5. 消除3行+3列的食物，包括锁链和冰块。`,
    Generation_Method6: `6. 将屏幕中随机一种食物变为火箭并释放。`,
    Generation_Method7: `7. 消除其中心向外延伸的24个食物，包括锁链和冰块。`,
    Generation_Method8: `8. 将屏幕中随机一种食物块变为炸弹并释放。`,
    Generation_Method9: `9. 消除屏幕中所有食物，包括锁链和冰块。`,

    //常驻任务
    Permanent_Task: `常驻任务`,
    Permanent_Task1: `玩家每消除10只螃蟹，便会获得两次随机消除一块2x2区域的奖励，若本次消除螃蟹满足两次及以上，则第二次开始只给一组2x2消除。`,

    //随机任务
    Random_Task: `随机任务`,
    Random_Task1: `根据人数增加所需消除的数量，比例为加一人则需多消除一个，例如2人局需消除7次，3人局需消除8次。`,
    Random_Task2: `- 每次最多同时存在1个任务。`,
    Random_Task3: `- 任务顺序每局开局随机生成一种，依次交替进行。`,
    Random_Task4: `- 消除7个指定颜色可获得一个随机彩。`,
    Random_Task5: `- 消除7个指定颜色可获得一个冰块。`,
    Random_Task6: `- 消除7个指定颜色可获得一个锁链。`,
    Random_Task7: `- 消除7个指定颜色可获得一个随机形态的火箭道具。`,

    //锁链简介
    Chains: `锁链`,
    Chains1: `1. 消除任务：游戏比赛开始后，每位玩家会收到系统派发的任务，完成任务会向其他玩家释放妨碍道具——锁链。`,
    Chains2: `2. 锁链的妨碍效果：随机锁住一个位置内的食物。受限制期间，锁链锁住的位置不能移动，锁链内的食物可能会因为其他地块的消除而改变，并非锁住固定食物。`,
    Chains3: `3. 锁链的解除：在锁链邻近位置（上下左右1格的块）进行普通消除后或者在道具有效区域内，消除1次即可解除。`,

    //冰块简介
    Ice_Blocks: `冰块`,
    Ice_Blocks1: `1. 消除任务：游戏比赛开始后，每位玩家会收到系统派发的任务，完成任务会向其他玩家释放妨碍道具——冰块。`,
    Ice_Blocks2: `2. 冰块的妨碍效果：随机冻住一个食物，受冻期间，位置可以被动移动，玩家不能主动移动。`,
    Ice_Blocks3: `3. 冰块的解除：在冰块邻近位置（上下左右1格的块）进行普通消除后或者在道具有效区域内,消除1次即可解除。`,

    //得分细则
    Scoring_Details: `得分细则`,
    Scoring_Details1: `普通消除：消除单块得分=20 分；`,
    Scoring_Details2: `连续消除：消除单块得分=连消次数x20 分;`,
    Scoring_Details3: `火箭消除：火箭激活得分=300分;由火箭消除的单块得分=30分;`,
    Scoring_Details4: `炸弹消除：炸弹激活得分=500分;由炸弹消除的单块得分=50分;`,
    Scoring_Details5: `彩虹消除：彩虹激活得分=600分;由彩虹消除的单块得分=60分;`,
    Scoring_Details6: `火箭组合消除：双火箭激活得分=1200分;由超级火箭消除的单块得分=60分;`,
    Scoring_Details7: `炸弹火箭组合消除：炸弹火箭组合激活得分=1600分由炸弹火箭消除的单块得分=80分;`,
    Scoring_Details8: `彩虹火箭组合消除：彩虹火箭组合激活得分=1800分由彩虹火箭消除的单块得分=90分;`,
    Scoring_Details9: `炸弹组合消除：超级炸弹激活得分=2000分;由超级炸弹消除的单块得分=100分;`,
    Scoring_Details10: `彩虹炸弹消除：彩虹炸弹激活得分=2200分;由彩虹炸弹消除的单块得分=110分;`,
    Scoring_Details11: `彩虹组合消除：超级彩虹激活得分=2400分;由超级彩虹消除的单块得分=120分;`,
};

const cocos = cc as any;
if (!cocos.Jou_i18n) cocos.Jou_i18n = {};
cocos.Jou_i18n.zh_CN = language;