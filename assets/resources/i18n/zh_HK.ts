export const language = {
//這部分是通用的
    kickout1: '您被請出房間',
    LeaveRoom: '房間已解散',
    InsufficientBalance: '餘額不足，去儲值',
    GameRouteNotFound: '遊戲路線異常',
    NetworkError: '網絡異常',
    RoomIsFull: '房間已滿',
    EnterRoomNumber: '輸入房間號',
    GetUserInfoFailed: '獲取用戶資訊失敗',
    RoomDoesNotExist: '房間不存在',
    FailedToDeductGoldCoins: '扣除金幣失敗',
    ExitApplication: '確定退出遊戲？',
    QuitTheGame: '退出後將無法返回遊戲。',

    NotEnoughPlayers: '玩家數量不足',
    TheGameIsFullOfPlayers: '玩家數量已滿',
    kickout2: '是否將 {0} 請出房間?',

    upSeat: '加入遊戲',
    downSeat: '退出遊戲',
    startGame: '開始',
    readyGame: '準備',
    cancelGame: '取消準備',
    cancel: '取消',
    confirm: '確定',
    kickout3: '踢出',
    back: '返回',
    leave: '退出',
    music: '音樂',
    sound: '音效',
    join:'進入', //加入
    create:'創建', //创建
    auto:'匹配',
    Room:'房間',
    room_number:'房號', //房间号
    copy:'複製', //复制
    game_amount:'遊戲費用', //游戏费用
    player_numbers:'玩家數量:', //玩家数量
    room_exist:'房間不存在',//房间不存在
    enter_room_number:'輸入房間號',//输入房间号
    free:'免費',
    players:'玩家', //玩家
    Player:'玩家',
    Tickets:'門票',
    Empty:'空位',

    nextlevel:'下一關', //下一關  
    relevel:'再玩一次', //再來一局 
   

    //幫助
    Tips: `幫助`,
    Tips1: `1. 每局遊戲比賽時間：120秒。`,
    Tips2: `2. 排名規則：遊戲時間結束後，根據玩家的得分高低進行排名，得分相同排名相同。`,
    Tips3: `3. 消除規則：通過移動遊戲中的食物與臨近的食物交換位置使相同的食物處於相鄰狀態，若滿足相鄰的同樣食物>=3個位置交換成功，則完成消除得分。`,
    Tips4: `4. 道具消除：完成特殊的消除會生成消除道具，道具和附近塊交換位置後會使用並消耗。`,
    Tips5: `5. 連續消除：玩家一次消除行為引發新食物產生，食物下落後觸發的消除為連續消除，連續消除期間不可操作。一次下落期間發生的連續消除記為1輪。`,

    //產生方式
    Generation_Method: `產生方式和使用效果`,
    Generation_Method1: `1. 消除整列或整行食物。`,
    Generation_Method2: `2. 消除其中心向外延伸的12個食物。`,
    Generation_Method3: `3. 消除螢幕中所有與該道具交換位置相同的食物。`,
    Generation_Method4: `4. 消除1行+1列的食物，包括鎖鏈和冰塊。`,
    Generation_Method5: `5. 消除3行+3列的食物，包括鎖鏈和冰塊。`,
    Generation_Method6: `6. 將螢幕中隨機一種食物變為火箭並釋放。`,
    Generation_Method7: `7. 消除其中心向外延伸的24個食物，包括鎖鏈和冰塊。`,
    Generation_Method8: `8. 將螢幕中隨機一種食物塊變為炸彈並釋放。`,
    Generation_Method9: `9. 消除螢幕中所有食物，包括鎖鏈和冰塊。`,

    //常駐任務
    Permanent_Task: `常駐任務`,
    Permanent_Task1: `玩家每消除10隻螃蟹，便會獲得兩次隨機消除一塊2x2區域的獎勵，若本次消除螃蟹滿足兩次及以上，則第二次開始只給一組2x2消除。`,

    //隨機任務
    Random_Task: `隨機任務`,
    Random_Task1: `根據人數增加所需消除的數量，比例為加一人則需多消除一個，例如2人局需消除7次，3人局需消除8次。`,
    Random_Task2: `- 每次最多同時存在1個任務。`,
    Random_Task3: `- 任務順序每局開局隨機生成一種，依次交替進行。`,
    Random_Task4: `- 消除7個指定顏色可獲得一個隨機彩。`,
    Random_Task5: `- 消除7個指定顏色可獲得一個冰塊。`,
    Random_Task6: `- 消除7個指定顏色可獲得一個鎖鏈。`,
    Random_Task7: `- 消除7個指定顏色可獲得一個隨機形態的火箭道具。`,

    //鎖鏈簡介
    Chains: `鎖鏈`,
    Chains1: `1. 消除任務：遊戲比賽開始後，每位玩家會收到系統派發的任務。完成任務會向其他玩家釋放妨礙道具——鎖鏈。`,
    Chains2: `2. 鎖鏈的妨礙效果：隨機鎖住一個位置內的食物。受限制期間，鎖鏈鎖住的位置不能移動，鎖鏈內的食物可能會因為其他地塊的消除而改變，並非鎖住固定食物。`,
    Chains3: `3. 鎖鏈的解除：在鎖鏈鄰近位置（上下左右1格的塊）進行普通消除後或者在道具有效區域內，消除1次即可解除。`,

    //冰塊簡介
    Ice_Blocks: `冰塊`,
    Ice_Blocks1: `1. 消除任務：遊戲比賽開始後，每位玩家會收到系統派發的任務。完成任務會向其他玩家釋放妨礙道具——冰塊。`,
    Ice_Blocks2: `2. 冰塊的妨礙效果：隨機凍住一個食物，受凍期間，位置可以被動移動，玩家不能主動移動。`,
    Ice_Blocks3: `3. 冰塊的解除：在冰塊鄰近位置（上下左右1格的塊）進行普通消除後或者在道具有效區域內,消除1次即可解除。`,

    // 得分細則
    Scoring_Details: `得分細則`,
    Scoring_Details1: `普通消除：消除單塊得分=20分;`,
    Scoring_Details2: `連續消除：消除單塊得分=連消次數x20分;`,
    Scoring_Details3: `火箭消除：火箭啟動得分=300分;由火箭消除的單塊得分=30分;`,
    Scoring_Details4: `炸彈消除：炸彈啟動得分=500分;由炸彈消除的單塊得分=50分;`,
    Scoring_Details5: `彩虹消除：彩虹啟動得分=600分;由彩虹消除的單塊得分=60分;`,
    Scoring_Details6: `火箭組合消除：雙火箭啟動得分=1200分;由超級火箭消除的單塊得分=60分;`,
    Scoring_Details7: `炸彈火箭組合消除：炸彈火箭組合啟動得分=1600分;由炸彈火箭消除的單塊得分=80分;`,
    Scoring_Details8: `彩虹火箭組合消除：彩虹火箭組合啟動得分=1800分;由彩虹火箭消除的單塊得分=90分;`,
    Scoring_Details9: `炸彈組合消除：超級炸彈啟動得分=2000分;由超級炸彈消除的單塊得分=100分;`,
    Scoring_Details10: `彩虹炸彈消除：彩虹炸彈啟動得分=2200分;由彩虹炸彈消除的單塊得分=110分;`,
    Scoring_Details11: `彩虹組合消除：超級彩虹啟動得分=2400分;由超級彩虹消除的單塊得分=120分;`,
};

const cocos = cc as any;
if (!cocos.Jou_i18n) cocos.Jou_i18n = {};
cocos.Jou_i18n.zh_HK = language;