// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { ScrollViewHelper } from "./ScrollViewHelper";

const { ccclass, property } = cc._decorator;

// 关卡状态枚举
export enum LevelStatus {
    LOCKED = 0,     // 未解锁（灰色）
    CURRENT = 1,    // 正在进行（黄色）
    COMPLETED = 2   // 已通关（绿色）
}

// 关卡数据接口
export interface LevelData {
    levelNumber: number;
    status: LevelStatus;
}

@ccclass
export default class LevelSelectController extends cc.Component {

    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;

    @property(cc.Node)
    content: cc.Node = null;

    // 关卡数据
    private levelDataList: LevelData[] = [];
    private currentSelectedLevel: number = 1;
    private totalLevels: number = 30;
    private screenWidth: number = 650;
    private levelItemWidth: number = 150; // 关卡项的宽度（包括间距）
    private visibleLevels: number = 3; // 可见关卡数量

    // 连接线配置（公开，方便调试）
    public lineCount: number = 9; // 每两个关卡之间的连接线数量
    public lineSpacing: number = 16; // 连接线之间的间距
    public levelToLineDistance: number = 8; // 关卡到连接线的距离

    // 关卡节点列表
    private levelNodes: cc.Node[] = [];

    // 滑动状态标志
    private isAutoScrolling: boolean = false;

    // 关卡选择变化回调
    public onLevelSelectionChanged: (levelNumber: number) => void = null;

    onLoad() {
        // 修复ScrollView的Scrollbar问题
        this.fixScrollViewScrollbar();

        this.initLevelData();
        this.createLevelItems();
        this.updateLevelDisplay();
        this.setupScrollEvents();

        // 设置初始滚动位置为第1关（最左边）
        if (this.scrollView) {
            this.scrollView.scrollToPercentHorizontal(0, 0);
        }
    }

    start() {
        // 延迟滚动到当前选中关卡，确保界面完全初始化
        this.scheduleOnce(() => {
            // 滚动到当前选中的关卡（可能已经通过ExtendLevelProgress设置了）
            cc.log(`LevelSelectController start() - 滚动到关卡 ${this.currentSelectedLevel}`);
            this.scrollToLevel(this.currentSelectedLevel);
        }, 0.1);
    }

    onDestroy() {
        // 移除事件监听器
        if (this.scrollView) {
            this.scrollView.node.off('scrolling', this.onScrolling, this);
            this.scrollView.node.off('scroll-ended', this.onScrollEnded, this);
        }
    }

    /**
     * 初始化关卡数据
     */
    private initLevelData() {
        this.levelDataList = [];

        // 初始化关卡状态：第1关为当前可玩关卡，其他为锁定状态
        for (let i = 1; i <= this.totalLevels; i++) {
            let status: LevelStatus;
            if (i === 1) {
                status = LevelStatus.CURRENT; // 第1关默认为当前可玩关卡（显示开始游戏按钮）
            } else {
                status = LevelStatus.LOCKED; // 其他关卡锁定
            }

            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }

        // 默认选中第一关
        this.currentSelectedLevel = 1;
    }

    /**
     * 创建关卡项目
     */
    private createLevelItems() {
        if (!this.content) {
            cc.error("Content node is not assigned!");
            return;
        }

        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];

        // 计算总宽度
        const totalWidth = (this.totalLevels - 1) * this.levelItemWidth + this.screenWidth;
        this.content.width = totalWidth;

        for (let i = 0; i < this.totalLevels; i++) {
            const levelData = this.levelDataList[i];
            
            // 创建关卡节点
            const levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);

            // 设置位置
            const posX = i * this.levelItemWidth - totalWidth / 2 + this.screenWidth / 2;
            levelNode.setPosition(posX, 0);

            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                this.createConnectionLines(i, posX);
            }
        }
    }

    /**
     * 创建关卡节点
     */
    private createLevelNode(levelData: LevelData): cc.Node {
        const node = new cc.Node(`Level_${levelData.levelNumber}`);
        
        // 添加Sprite组件
        const sprite = node.addComponent(cc.Sprite);
        
        // 添加Label组件显示关卡数字
        const labelNode = new cc.Node("LevelLabel");
        const label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();

        // 设置字体样式
        label.fontSize = 30;
        label.node.color = cc.color(255, 255, 255); // #FFFFFF
        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        label.verticalAlign = cc.Label.VerticalAlign.CENTER;

        // 添加外边框
        const outline = label.addComponent(cc.LabelOutline);
        this.updateLabelOutline(outline, levelData.status);

        labelNode.parent = node;
        labelNode.setPosition(0, 0); // 居中对齐

        // 添加Button组件
        const button = node.addComponent(cc.Button);
        button.target = node;

        // 设置点击事件
        const eventHandler = new cc.Component.EventHandler();
        eventHandler.target = this.node;
        eventHandler.component = "LevelSelectController";
        eventHandler.handler = "onLevelClicked";
        eventHandler.customEventData = levelData.levelNumber.toString();
        button.clickEvents.push(eventHandler);

        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);

        return node;
    }

    /**
     * 创建连接线组（9个连接线）
     */
    private createConnectionLines(levelIndex: number, levelPosX: number) {
        const startX = levelPosX + 46/2 + this.levelToLineDistance; // 关卡右边缘 + 距离
        const totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        const endX = levelPosX + this.levelItemWidth - 46/2 - this.levelToLineDistance; // 下一个关卡左边缘 - 距离
        const availableWidth = endX - startX;

        // 如果可用宽度小于需要的宽度，调整间距
        let actualSpacing = this.lineSpacing;
        if (totalLineWidth > availableWidth) {
            actualSpacing = availableWidth / (this.lineCount - 1);
        }

        for (let i = 0; i < this.lineCount; i++) {
            const lineNode = this.createSingleLineNode();
            this.content.addChild(lineNode);

            const lineX = startX + i * actualSpacing;
            lineNode.setPosition(lineX, 0);
        }
    }

    /**
     * 创建单个连接线节点
     */
    private createSingleLineNode(): cc.Node {
        const node = new cc.Node("Line");
        const sprite = node.addComponent(cc.Sprite);

        // 设置连接线大小为6*6
        node.setContentSize(6, 6);

        // 加载连接线图片
        cc.resources.load("hall_page_res/Level_Btn/pop_line", cc.SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame as cc.SpriteFrame;
                // 图片加载后重新设置大小为6x6，确保不被图片原始大小覆盖
                node.setContentSize(6, 6);
            }
        });

        return node;
    }

    /**
     * 检查是否为特殊关卡（第5、10、15、20、25关）
     */
    private isSpecialLevel(levelNumber: number): boolean {
        return levelNumber % 5 === 0;
    }

    /**
     * 更新关卡节点外观
     */
    private updateLevelNodeAppearance(node: cc.Node, levelData: LevelData, isSelected: boolean) {
        const sprite = node.getComponent(cc.Sprite);
        if (!sprite) return;

        let imagePath = "";
        let size = cc.size(46, 46);

        // 检查是否为特殊关卡（第5、10、15、20、25关）
        const isSpecialLevel = this.isSpecialLevel(levelData.levelNumber);
        const uiSuffix = isSpecialLevel ? "01" : "";


        // 根据状态和是否选中确定图片路径
        if (isSelected) {
            size = cc.size(86, 86);
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    imagePath = `hall_page_res/Level_Btn/pop_gray${uiSuffix}_choose`;
                    break;
                case LevelStatus.CURRENT:
                    imagePath = `hall_page_res/Level_Btn/pop_yellow${uiSuffix}_choose`;
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = `hall_page_res/Level_Btn/pop_green${uiSuffix}_choose`;
                    break;
            }
        } else {
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    imagePath = `hall_page_res/Level_Btn/pop_gray${uiSuffix}`;
                    break;
                case LevelStatus.CURRENT:
                    imagePath = `hall_page_res/Level_Btn/pop_yellow${uiSuffix}`;
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = `hall_page_res/Level_Btn/pop_green${uiSuffix}`;
                    break;
            }
        }

        // 设置节点大小
        node.setContentSize(size);

        // 加载并设置图片
        cc.resources.load(imagePath, cc.SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame as cc.SpriteFrame;
                // 图片加载后强制设置正确的大小，覆盖图片原始大小
                node.setContentSize(size);
            }
        });

        // 更新标签外边框
        const labelNode = node.getChildByName("LevelLabel");
        if (labelNode) {
            const label = labelNode.getComponent(cc.Label);
            if (label) {
                const outline = label.getComponent(cc.LabelOutline);
                if (outline) {
                    this.updateLabelOutline(outline, levelData.status);
                }
            }
        }
    }

    /**
     * 更新关卡显示
     */
    private updateLevelDisplay() {
        for (let i = 0; i < this.levelNodes.length; i++) {
            const node = this.levelNodes[i];
            const levelData = this.levelDataList[i];
            const isSelected = (levelData.levelNumber === this.currentSelectedLevel);
            
            this.updateLevelNodeAppearance(node, levelData, isSelected);
        }
    }

    /**
     * 滚动到指定关卡
     */
    private scrollToLevel(levelNumber: number) {
        if (levelNumber < 1 || levelNumber > this.totalLevels) return;

        const targetIndex = levelNumber - 1;
        const contentWidth = this.content.width;
        const scrollViewWidth = this.scrollView.node.width;
        
        // 计算目标位置的偏移量
        const targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);
        
        // 限制偏移量在有效范围内
        const clampedOffset = cc.misc.clampf(targetOffset, 0, 1);
        
        // 设置滚动位置
        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);
    }

    /**
     * 关卡点击事件处理
     */
    public onLevelClicked(event: cc.Event, customEventData: string) {
        const levelNumber = parseInt(customEventData);

        // 允许选择任何关卡（包括未解锁的）
        // 更新当前选中关卡
        this.currentSelectedLevel = levelNumber;
        this.updateLevelDisplay();

        // 滚动到选中关卡
        this.isAutoScrolling = true;
        this.scrollToLevel(levelNumber);

      

        // 通知关卡选择变化
        if (this.onLevelSelectionChanged) {
            this.onLevelSelectionChanged(levelNumber);
        }

        // 这里可以添加进入关卡的逻辑
        // this.enterLevel(levelNumber);
    }

    /**
     * 设置关卡状态
     */
    public setLevelStatus(levelNumber: number, status: LevelStatus) {
        if (levelNumber < 1 || levelNumber > this.totalLevels) return;

        this.levelDataList[levelNumber - 1].status = status;
        this.updateLevelDisplay();
    }

    /**
     * 获取当前选中的关卡
     */
    public getCurrentSelectedLevel(): number {
        return this.currentSelectedLevel;
    }

    /**
     * 获取指定关卡的数据
     */
    public getLevelData(levelNumber: number): LevelData | null {
        if (levelNumber < 1 || levelNumber > this.totalLevels) return null;
        return this.levelDataList[levelNumber - 1];
    }

    /**
     * 设置关卡进度（从外部调用，比如从后端获取数据后）
     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels
     */
    public setLevelProgress(levelProgressData: any) {
        cc.log(`📨 LevelSelectController.setLevelProgress() 被调用`);
        cc.log(`   - 接收到的数据:`, levelProgressData);

        if (!levelProgressData) {
            cc.warn("❌ levelProgressData 为空");
            return;
        }

        const { clearedLevels, currentLevel, totalLevels } = levelProgressData;
        cc.log(`   - clearedLevels: ${clearedLevels}, currentLevel: ${currentLevel}, totalLevels: ${totalLevels}`);

        // 验证数据有效性
        if (clearedLevels < 0 || currentLevel < 1 || currentLevel > totalLevels) {
            cc.warn(`❌ 数据验证失败: clearedLevels=${clearedLevels}, currentLevel=${currentLevel}, totalLevels=${totalLevels}`);
            return;
        }

        // 更新总关卡数（如果后端传了的话）
        if (totalLevels && totalLevels > 0) {
            this.totalLevels = totalLevels;
        }

        // 重新设置所有关卡状态
        for (let i = 1; i <= this.totalLevels; i++) {
            let status: LevelStatus;
            if (i < currentLevel) {
                status = LevelStatus.COMPLETED; // 当前关卡之前的都是已完成（绿色）
            } else if (i === currentLevel) {
                status = LevelStatus.CURRENT; // 当前关卡（黄色选择UI）
            } else {
                status = LevelStatus.LOCKED; // 当前关卡之后的都是未解锁（灰色）
            }

            this.levelDataList[i - 1].status = status;
        }

        // 更新当前选中关卡为后端指定的currentLevel
        cc.log(`🎯 设置当前选中关卡: ${this.currentSelectedLevel} -> ${currentLevel}`);
        this.currentSelectedLevel = currentLevel;
        this.updateLevelDisplay();

        cc.log(`📜 滚动到关卡 ${currentLevel}`);
        this.scrollToLevel(currentLevel);

        // 通知关卡选择变化
        if (this.onLevelSelectionChanged) {
            cc.log(`🔔 通知关卡选择变化: ${currentLevel}`);
            this.onLevelSelectionChanged(currentLevel);
        }
    }

    /**
     * 设置关卡进度（兼容旧接口）
     * @param completedLevels 已完成的关卡数
     */
    public setLevelProgressLegacy(completedLevels: number) {
        // 转换为新的数据格式
        const levelProgressData = {
            clearedLevels: completedLevels,
            currentLevel: Math.min(completedLevels + 1, this.totalLevels),
            totalLevels: this.totalLevels
        };
        this.setLevelProgress(levelProgressData);
    }

    /**
     * 解锁下一关
     */
    public unlockNextLevel() {
        for (let i = 0; i < this.levelDataList.length; i++) {
            if (this.levelDataList[i].status === LevelStatus.LOCKED) {
                this.levelDataList[i].status = LevelStatus.CURRENT;
                this.updateLevelDisplay();
                break;
            }
        }
    }

    /**
     * 完成当前关卡
     */
    public completeCurrentLevel() {
        const currentLevelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (currentLevelData.status === LevelStatus.CURRENT) {
            currentLevelData.status = LevelStatus.COMPLETED;
            this.unlockNextLevel();
            this.updateLevelDisplay();
        }
    }

    /**
     * 设置滑动事件监听
     */
    private setupScrollEvents() {
        if (!this.scrollView) return;

        // 监听滑动中事件
        this.scrollView.node.on('scrolling', this.onScrolling, this);

        // 监听滑动结束事件
        this.scrollView.node.on('scroll-ended', this.onScrollEnded, this);

     
    }

    /**
     * 滑动中事件处理
     */
    private onScrolling() {
        this.updateSelectedLevelByPosition();
    }

    /**
     * 滑动结束事件处理
     */
    private onScrollEnded() {
        // 如果是自动滚动触发的事件，忽略
        if (this.isAutoScrolling) {
            this.isAutoScrolling = false;
            return;
        }

        this.updateSelectedLevelByPosition();
        // 滑动结束后，将选中的关卡固定居中
        this.isAutoScrolling = true;
        this.scrollToLevel(this.currentSelectedLevel);
    }

    /**
     * 根据当前位置更新选中的关卡
     */
    private updateSelectedLevelByPosition() {
        if (!this.scrollView || !this.content) return;

        // 获取ScrollView的中心位置（相对于ScrollView节点）
        const scrollViewCenterX = 0; // ScrollView的中心就是x=0

        // 找到最接近ScrollView中心位置的关卡
        let closestLevel = 1;
        let minDistance = Number.MAX_VALUE;

        for (let i = 0; i < this.levelNodes.length; i++) {
            const levelNode = this.levelNodes[i];

            // 将关卡节点的本地坐标转换为ScrollView坐标系
            const levelWorldPos = this.content.convertToWorldSpaceAR(levelNode.position);
            const levelScrollViewPos = this.scrollView.node.convertToNodeSpaceAR(levelWorldPos);

            // 计算关卡与ScrollView中心的距离
            const distance = Math.abs(levelScrollViewPos.x - scrollViewCenterX);

            if (distance < minDistance) {
                minDistance = distance;
                closestLevel = i + 1;
            }
        }

        // 如果选中的关卡发生变化，更新显示
        if (closestLevel !== this.currentSelectedLevel) {
            this.currentSelectedLevel = closestLevel;
            this.updateLevelDisplay();
           

            // 通知关卡选择变化
            if (this.onLevelSelectionChanged) {
                this.onLevelSelectionChanged(closestLevel);
            }
        }
    }

    /**
     * 修复ScrollView的Scrollbar问题
     */
    private fixScrollViewScrollbar() {
        if (this.scrollView && this.content) {
            ScrollViewHelper.setupHorizontalScrollView(this.scrollView, this.content);
            ScrollViewHelper.debugScrollView(this.scrollView, "LevelSelectController");
        }
    }

    /**
     * 调试参数信息
     */
    public debugParameters() {
       

        // 计算连接线总宽度
        const totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
      

        // 计算可用宽度
        const availableWidth = this.levelItemWidth - 46 - (this.levelToLineDistance * 2);
      

        if (totalLineWidth > availableWidth) {
            const adjustedSpacing = availableWidth / (this.lineCount - 1);
           
        } else {
   
        }

      
    }

    /**
     * 更新标签外边框
     */
    private updateLabelOutline(outline: cc.LabelOutline, status: LevelStatus) {
        let outlineColor: cc.Color;
        switch (status) {
            case LevelStatus.LOCKED:
                // 未解锁边框为 #7B7B7B
                outlineColor = cc.color(123, 123, 123);
                break;
            case LevelStatus.CURRENT:
                // 当前玩到的关卡边框 #CF5800
                outlineColor = cc.color(207, 88, 0);
                break;
            case LevelStatus.COMPLETED:
                // 已解锁边框为 #119C0F
                outlineColor = cc.color(17, 156, 15);
                break;
            default:
                outlineColor = cc.color(123, 123, 123);
                break;
        }

        outline.color = outlineColor;
        outline.width = 1;
    }

    /**
     * 重新创建关卡项目（用于参数调整后刷新）
     */
    public refreshLevelItems() {
        this.createLevelItems();
        this.updateLevelDisplay();
        this.scrollToLevel(this.currentSelectedLevel);
    }


}
