// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { LevelStatus } from "./Level/LevelSelectController";

const { ccclass, property } = cc._decorator;

/**
 * 关卡选择演示控制器
 * 这个脚本展示了如何创建一个完整的关卡选择界面
 */
@ccclass
export default class LevelSelectDemo extends cc.Component {

    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;

    @property(cc.Node)
    content: cc.Node = null;

    @property(cc.Label)
    infoLabel: cc.Label = null;

    // 关卡数据
    private levelDataList: any[] = [];
    private currentSelectedLevel: number = 1;
    private totalLevels: number = 30;
    private levelItemWidth: number = 150;

    // 关卡节点列表
    private levelNodes: cc.Node[] = [];

    onLoad() {
        this.initLevelData();
        this.createLevelSelectUI();
    }

    start() {
        this.scrollToLevel(this.currentSelectedLevel);
        this.updateInfoDisplay();
    }

    /**
     * 初始化关卡数据
     */
    private initLevelData() {
        this.levelDataList = [];
        for (let i = 1; i <= this.totalLevels; i++) {
            let status: LevelStatus;
            if (i === 1) {
                status = LevelStatus.CURRENT; // 第一关为当前关卡
            } else if (i <= 3) {
                status = LevelStatus.COMPLETED; // 前3关已通关（示例）
            } else {
                status = LevelStatus.LOCKED; // 其他关卡未解锁
            }

            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }
    }

    /**
     * 创建关卡选择UI
     */
    private createLevelSelectUI() {
        if (!this.content) {
            cc.error("Content node is not assigned!");
            return;
        }

        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];

        // 计算总宽度
        const totalWidth = (this.totalLevels - 1) * this.levelItemWidth + 650;
        this.content.width = totalWidth;

        for (let i = 0; i < this.totalLevels; i++) {
            const levelData = this.levelDataList[i];
            
            // 创建关卡节点
            const levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);

            // 设置位置
            const posX = i * this.levelItemWidth - totalWidth / 2 + 650 / 2;
            levelNode.setPosition(posX, 0);

            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                const lineNode = this.createLineNode();
                this.content.addChild(lineNode);
                lineNode.setPosition(posX + this.levelItemWidth / 2, 0);
            }
        }
    }

    /**
     * 创建关卡节点
     */
    private createLevelNode(levelData: any): cc.Node {
        const node = new cc.Node(`Level_${levelData.levelNumber}`);
        
        // 添加Sprite组件
        node.addComponent(cc.Sprite);
        
        // 添加Label组件显示关卡数字
        const labelNode = new cc.Node("LevelLabel");
        const label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();
        label.fontSize = 20;
        label.node.color = cc.Color.WHITE;
        labelNode.parent = node;

        // 添加Button组件
        const button = node.addComponent(cc.Button);
        button.target = node;

        // 设置点击事件
        node.on('click', () => {
            this.onLevelClicked(levelData.levelNumber);
        }, this);

        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);

        return node;
    }

    /**
     * 创建连接线节点
     */
    private createLineNode(): cc.Node {
        const node = new cc.Node("Line");
        const sprite = node.addComponent(cc.Sprite);
        
        // 加载连接线图片
        cc.resources.load("hall_page_res/Level_Btn/pop_line", cc.SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame as cc.SpriteFrame;
            }
        });

        return node;
    }

    /**
     * 更新关卡节点外观
     */
    private updateLevelNodeAppearance(node: cc.Node, levelData: any, isSelected: boolean) {
        const sprite = node.getComponent(cc.Sprite);
        if (!sprite) return;

        let imagePath = "";
        let size = cc.size(46, 46);

        // 根据状态和是否选中确定图片路径
        if (isSelected) {
            size = cc.size(86, 86);
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray_choose";
                    break;
                case LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow_choose";
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green_choose";
                    break;
            }
        } else {
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray";
                    break;
                case LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow";
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green";
                    break;
            }
        }

        // 设置节点大小
        node.setContentSize(size);

        // 加载并设置图片
        cc.resources.load(imagePath, cc.SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame as cc.SpriteFrame;
            }
        });
    }

    /**
     * 更新所有关卡显示
     */
    private updateAllLevelsDisplay() {
        for (let i = 0; i < this.levelNodes.length; i++) {
            const node = this.levelNodes[i];
            const levelData = this.levelDataList[i];
            const isSelected = (levelData.levelNumber === this.currentSelectedLevel);
            
            this.updateLevelNodeAppearance(node, levelData, isSelected);
        }
    }

    /**
     * 滚动到指定关卡
     */
    private scrollToLevel(levelNumber: number) {
        if (levelNumber < 1 || levelNumber > this.totalLevels) return;

        const targetIndex = levelNumber - 1;
        const contentWidth = this.content.width;
        const scrollViewWidth = this.scrollView.node.width;
        
        // 计算目标位置的偏移量
        const targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);
        
        // 限制偏移量在有效范围内
        const clampedOffset = cc.misc.clampf(targetOffset, 0, 1);
        
        // 设置滚动位置
        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);
    }

    /**
     * 关卡点击事件处理
     */
    private onLevelClicked(levelNumber: number) {
        // 检查关卡是否可以选择（未锁定）
        const levelData = this.levelDataList[levelNumber - 1];
        if (levelData.status === LevelStatus.LOCKED) {
            cc.log(`关卡 ${levelNumber} 未解锁！`);
            return;
        }

        // 更新当前选中关卡
        this.currentSelectedLevel = levelNumber;
        this.updateAllLevelsDisplay();
        
        // 滚动到选中关卡
        this.scrollToLevel(levelNumber);

        this.updateInfoDisplay();
        cc.log(`选择关卡: ${levelNumber}`);
    }

    /**
     * 更新信息显示
     */
    private updateInfoDisplay() {
        if (this.infoLabel) {
            const levelData = this.levelDataList[this.currentSelectedLevel - 1];
            let statusText = "";
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    statusText = "未解锁";
                    break;
                case LevelStatus.CURRENT:
                    statusText = "进行中";
                    break;
                case LevelStatus.COMPLETED:
                    statusText = "已通关";
                    break;
            }
            this.infoLabel.string = `当前选中: 关卡${this.currentSelectedLevel} (${statusText})`;
        }
    }

    /**
     * 完成当前关卡（测试用）
     */
    public completeCurrentLevel() {
        const levelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (levelData.status === LevelStatus.CURRENT) {
            levelData.status = LevelStatus.COMPLETED;
            
            // 解锁下一关
            if (this.currentSelectedLevel < this.totalLevels) {
                const nextLevelData = this.levelDataList[this.currentSelectedLevel];
                if (nextLevelData.status === LevelStatus.LOCKED) {
                    nextLevelData.status = LevelStatus.CURRENT;
                }
            }
            
            this.updateAllLevelsDisplay();
            this.updateInfoDisplay();
        }
    }
}
