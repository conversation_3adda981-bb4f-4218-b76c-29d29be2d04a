{"version": 3, "sources": ["assets/scripts/hall/InfoDialogController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAKtF,yCAAwC;AACxC,uCAAsC;AAEhC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C,UAAU;AAEV;IAAkD,wCAAY;IAA9D;QAAA,qEAkXC;QA/WG,aAAO,GAAY,IAAI,CAAA;QAEvB,mBAAa,GAAY,IAAI,CAAA;QAE7B,gBAAU,GAAY,IAAI,CAAA;QAE1B,cAAQ,GAAc,IAAI,CAAC;QAE3B,eAAS,GAAc,IAAI,CAAC;QAG5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,iBAAW,GAAc,IAAI,CAAA;QAE7B,iBAAW,GAAc,IAAI,CAAA;QAE7B,WAAW;QAEX,qBAAe,GAAY,IAAI,CAAA,CAAC,iBAAiB;QAGjD,sBAAgB,GAAY,IAAI,CAAA,CAAC,iBAAiB;QAGlD,gBAAU,GAAY,IAAI,CAAA,CAAC,aAAa;QAGxC,iBAAW,GAAY,IAAI,CAAA,CAAC,aAAa;QAEzC,0BAA0B;QAClB,qBAAe,GAAW,CAAC,CAAA;QAEnC,eAAS,GAAa,EAAE,CAAA,CAAA,WAAW;QACnC,cAAQ,GAAa,EAAE,CAAA;QACvB,0BAAoB,GAAa,EAAE,CAAA;QACnC,mBAAa,GAAa,EAAE,CAAA;QAC5B,gBAAU,GAAa,EAAE,CAAA;QACzB,gBAAU,GAAa,EAAE,CAAA;QACzB,aAAO,GAAa,EAAE,CAAA;QACtB,oBAAc,GAAa,EAAE,CAAA;QAE7B,mBAAa,GAAgB,EAAE,CAAA;QAG/B,kBAAY,GAAa,IAAI,CAAA,CAAC,SAAS;;QAiTvC,iBAAiB;IACrB,CAAC;IAhTG,qCAAM,GAAN;QAEA,6BAA6B;QAC7B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,4BAA4B;QAC5B,QAAQ;QAGR,yBAAyB;QACzB,0CAA0C;QAC1C,uDAAuD;QACvD,oDAAoD;QACpD,iDAAiD;QACjD,4CAA4C;QAC5C,gDAAgD;QAChD,qDAAqD;QACrD,mBAAmB;QACnB,yBAAyB;QACzB,2CAA2C;QAC3C,2CAA2C;QAC3C,2CAA2C;QAC3C,2CAA2C;QAC3C,2CAA2C;QAC3C,QAAQ;QACR,oCAAoC;QACpC,wDAAwD;QACxD,wDAAwD;QACxD,wDAAwD;QACxD,wDAAwD;QACxD,wDAAwD;QACxD,wDAAwD;QACxD,wDAAwD;QACxD,wDAAwD;QACxD,wDAAwD;QACxD,QAAQ;QACR,6BAA6B;QAC7B,qDAAqD;QACrD,QAAQ;QACR,0BAA0B;QAC1B,kDAAkD;QAClD,kDAAkD;QAClD,kDAAkD;QAClD,kDAAkD;QAClD,kDAAkD;QAClD,kDAAkD;QAClD,kDAAkD;QAClD,QAAQ;QACR,0BAA0B;QAC1B,6CAA6C;QAC7C,6CAA6C;QAC7C,6CAA6C;QAC7C,QAAQ;QACR,uBAAuB;QACvB,iDAAiD;QACjD,iDAAiD;QACjD,iDAAiD;QACjD,QAAQ;QACR,8BAA8B;QAC9B,sDAAsD;QACtD,sDAAsD;QACtD,sDAAsD;QACtD,sDAAsD;QACtD,sDAAsD;QACtD,sDAAsD;QACtD,sDAAsD;QACtD,sDAAsD;QACtD,sDAAsD;QACtD,uDAAuD;QACvD,uDAAuD;QACvD,QAAQ;QAEJ,UAAU;QACV,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAE7B,CAAC;IAED,oCAAK,GAAL;QAAA,iBA8EC;QA5EG,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,EAAE,eAAM,CAAC,SAAS,GAAG,wBAAwB,EAAE,eAAM,CAAC,SAAS,GAAG,yBAAyB,EAAE;YAClI,KAAI,CAAC,IAAI,EAAE,CAAA;QACf,CAAC,CAAC,CAAC;QAEP,0CAA0C;QAE1C,2CAA2C;QAC3C,gDAAgD;QAChD,mEAAmE;QACnE,mFAAmF;QACnF,+CAA+C;QAC/C,6CAA6C;QAC7C,SAAS;QAET,2CAA2C;QAC3C,4DAA4D;QAC5D,mEAAmE;QACnE,mFAAmF;QACnF,+CAA+C;QAE/C,kEAAkE;QAClE,oDAAoD;QAEpD,6CAA6C;QAC7C,SAAS;QACT,2CAA2C;QAC3C,qDAAqD;QACrD,mEAAmE;QACnE,mFAAmF;QACnF,+CAA+C;QAC/C,6CAA6C;QAC7C,SAAS;QACT,qDAAqD;QACrD,yCAAyC;QAGzC,2CAA2C;QAC3C,kDAAkD;QAClD,mEAAmE;QACnE,mFAAmF;QACnF,+CAA+C;QAC/C,6CAA6C;QAC7C,SAAS;QACT,qDAAqD;QACrD,yCAAyC;QAEzC,2CAA2C;QAC3C,kDAAkD;QAClD,mEAAmE;QACnE,mFAAmF;QACnF,+CAA+C;QAC/C,6CAA6C;QAC7C,SAAS;QACT,2CAA2C;QAC3C,+CAA+C;QAC/C,mEAAmE;QACnE,mFAAmF;QACnF,+CAA+C;QAC/C,6CAA6C;QAC7C,SAAS;QACT,2CAA2C;QAC3C,sDAAsD;QACtD,mEAAmE;QACnE,mFAAmF;QACnF,+CAA+C;QAC/C,6CAA6C;QAC7C,SAAS;QACT,IAAI;QAGJ,gCAAgC;QAChC,8DAA8D;QAC9D,yEAAyE;QACzE,2CAA2C;QAC3C,yCAAyC;QACzC,IAAI;IACJ,CAAC;IAID,mCAAI,GAAJ,UAAK,YAAsB;QACvB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAA;QACtB,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACxC,KAAK,EAAE,CAAC;IACjB,CAAC;IACD,mCAAI,GAAJ;QAAA,iBAWC;QAVG,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,EAAE,CAAA;SACtB;QACD,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACxC,IAAI,CAAC;YACF,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QAC5B,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,gDAAiB,GAAzB;QACI,SAAS;QACT,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;SACzC;QACD,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;SACzC;QAED,WAAW;QACX,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;SAC7D;QACD,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;SAC/D;QAED,qBAAqB;QACrB,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,gDAAiB,GAAzB;QACI,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACnB,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,iDAAkB,GAA1B;QACI,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACnB,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACK,+CAAgB,GAAxB,UAAyB,QAAgB;QACrC,IAAI,IAAI,CAAC,eAAe,KAAK,QAAQ,EAAE;YACnC,OAAO,CAAC,gBAAgB;SAC3B;QAED,EAAE,CAAC,GAAG,CAAC,2CAAW,IAAI,CAAC,eAAe,YAAO,QAAU,CAAC,CAAC;QACzD,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;QAEhC,WAAW;QACX,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IAED;;;OAGG;IACK,gDAAiB,GAAzB,UAA0B,cAAsB;QAAhD,iBAsDC;QArDG,IAAM,iBAAiB,GAAG,GAAG,CAAC,CAAC,SAAS;QAExC,IAAI,cAAc,KAAK,CAAC,EAAE;YACtB,UAAU;YACV,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YAExB,mBAAmB;YACnB,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC;gBACnC,QAAQ;gBACR,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,GAAG,CAAC;gBAC7B,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC;qBACzB,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;qBACvD,KAAK,EAAE,CAAC;aAChB;YAED,mBAAmB;YACnB,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,OAAO;gBACP,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC;qBAC1B,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;qBAC1D,IAAI,CAAC;oBACF,KAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,KAAK,CAAC;oBACrC,KAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO;gBACxC,CAAC,CAAC;qBACD,KAAK,EAAE,CAAC;aAChB;SACJ;aAAM;YACH,UAAU;YACV,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YAExB,mBAAmB;YACnB,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC;gBACpC,QAAQ;gBACR,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,GAAG,CAAC;gBAC9B,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC;qBAC1B,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;qBACvD,KAAK,EAAE,CAAC;aAChB;YAED,mBAAmB;YACnB,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,OAAO;gBACP,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC;qBACzB,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;qBAC1D,IAAI,CAAC;oBACF,KAAI,CAAC,eAAe,CAAC,MAAM,GAAG,KAAK,CAAC;oBACpC,KAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO;gBACvC,CAAC,CAAC;qBACD,KAAK,EAAE,CAAC;aAChB;SACJ;IACL,CAAC;IA5WD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;yDACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;+DACW;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;4DACQ;IAE1B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;0DACO;IAE3B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;2DACQ;IAG5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;6DACS;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;6DACS;IAI7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;iEACa;IAG/B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;kEACc;IAGhC;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;4DACQ;IAG1B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACS;IA/CV,oBAAoB;QADxC,OAAO;OACa,oBAAoB,CAkXxC;IAAD,2BAAC;CAlXD,AAkXC,CAlXiD,EAAE,CAAC,SAAS,GAkX7D;kBAlXoB,oBAAoB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\n\nimport InfoItemController from \"../pfb/InfoItemController\";\nimport InfoItemOneController from \"../pfb/InfoItemOneController\";\nimport { Config } from \"../util/Config\";\nimport { Tools } from \"../util/Tools\";\n\nconst { ccclass, property } = cc._decorator;\n\n\n//游戏道具介绍页面\n@ccclass\nexport default class InfoDialogController extends cc.Component {\n\n    @property(cc.Node)\n    boardBg: cc.Node = null\n    @property(cc.Node)\n    boardBtnClose: cc.Node = null\n    @property(cc.Node)\n    contentLay: cc.Node = null\n    @property(cc.Prefab)\n    infoItem: cc.Prefab = null;\n    @property(cc.Prefab)\n    infoItem1: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    infoImage1: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage2: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage3: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage4: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage5: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage6: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage7: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage8: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage9: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage10: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage11: cc.Prefab = null\n\n    // 规则界面相关节点\n    @property(cc.Node)\n    danjiScrollView: cc.Node = null // 单机规则ScrollView\n\n    @property(cc.Node)\n    duorenScrollView: cc.Node = null // 联机规则ScrollView\n\n    @property(cc.Node)\n    leftButton: cc.Node = null // 左边按钮（单机规则）\n\n    @property(cc.Node)\n    rightButton: cc.Node = null // 右边按钮（联机规则）\n\n    // 当前显示的规则类型：0=单机规则，1=联机规则\n    private currentRuleType: number = 0\n\n    titleList: string[] = []//title 的列表\n    tipsList: string[] = []\n    generationMethodList: string[] = []\n    permanentList: string[] = []\n    randomList: string[] = []\n    chainsList: string[] = []\n    iceList: string[] = []\n    scoringDetails: string[] = []\n\n    infoImageList: cc.Prefab[] = []\n\n\n    backCallback: Function = null //隐藏弹窗的回调\n\n    onLoad() {\n\n    //     this.infoImageList = [\n    //         this.infoImage3,\n    //         this.infoImage4,\n    //         this.infoImage5,\n    //         this.infoImage6,\n    //         this.infoImage7,\n    //         this.infoImage8,\n    //         this.infoImage9,\n    //         this.infoImage10,\n    //         this.infoImage11,\n    //     ]\n\n\n    //     this.titleList = [\n    //         window.getLocalizedStr('Tips'),\n    //         window.getLocalizedStr('Generation_Method'),\n    //         window.getLocalizedStr('Permanent_Task'),\n    //         window.getLocalizedStr('Random_Task'),\n    //         window.getLocalizedStr('Chains'),\n    //         window.getLocalizedStr('Ice_Blocks'),\n    //         window.getLocalizedStr('Scoring_Details'),\n    //     ]//title 的列表\n    //     this. tipsList = [\n    //         window.getLocalizedStr('Tips1'),\n    //         window.getLocalizedStr('Tips2'),\n    //         window.getLocalizedStr('Tips3'),\n    //         window.getLocalizedStr('Tips4'),\n    //         window.getLocalizedStr('Tips5'),\n    //     ]\n    //     this.generationMethodList = [\n    //         window.getLocalizedStr('Generation_Method1'),\n    //         window.getLocalizedStr('Generation_Method2'),\n    //         window.getLocalizedStr('Generation_Method3'),\n    //         window.getLocalizedStr('Generation_Method4'),\n    //         window.getLocalizedStr('Generation_Method5'),\n    //         window.getLocalizedStr('Generation_Method6'),\n    //         window.getLocalizedStr('Generation_Method7'),\n    //         window.getLocalizedStr('Generation_Method8'),\n    //         window.getLocalizedStr('Generation_Method9'),\n    //     ]\n    //     this.permanentList = [\n    //         window.getLocalizedStr('Permanent_Task1'),\n    //     ]\n    //     this.randomList = [\n    //         window.getLocalizedStr('Random_Task1'),\n    //         window.getLocalizedStr('Random_Task2'),\n    //         window.getLocalizedStr('Random_Task3'),\n    //         window.getLocalizedStr('Random_Task4'),\n    //         window.getLocalizedStr('Random_Task5'),\n    //         window.getLocalizedStr('Random_Task6'),\n    //         window.getLocalizedStr('Random_Task7'),\n    //     ]\n    //     this.chainsList = [\n    //         window.getLocalizedStr('Chains1'),\n    //         window.getLocalizedStr('Chains2'),\n    //         window.getLocalizedStr('Chains3'),\n    //     ]\n    //     this.iceList = [\n    //         window.getLocalizedStr('Ice_Blocks1'),\n    //         window.getLocalizedStr('Ice_Blocks2'),\n    //         window.getLocalizedStr('Ice_Blocks3'),\n    //     ]\n    //     this.scoringDetails = [\n    //         window.getLocalizedStr('Scoring_Details1'),\n    //         window.getLocalizedStr('Scoring_Details2'),\n    //         window.getLocalizedStr('Scoring_Details3'),\n    //         window.getLocalizedStr('Scoring_Details4'),\n    //         window.getLocalizedStr('Scoring_Details5'),\n    //         window.getLocalizedStr('Scoring_Details6'),\n    //         window.getLocalizedStr('Scoring_Details7'),\n    //         window.getLocalizedStr('Scoring_Details8'),\n    //         window.getLocalizedStr('Scoring_Details9'),\n    //         window.getLocalizedStr('Scoring_Details10'),\n    //         window.getLocalizedStr('Scoring_Details11'),\n    //     ]\n\n        // 初始化规则界面\n        this.initRuleInterface();\n\n    }\n\n    start() {\n\n        Tools.imageButtonClick(this.boardBtnClose, Config.buttonRes + 'board_btn_close_normal', Config.buttonRes + 'board_btn_close_pressed', () => {\n            this.hide()\n        });\n\n    //     this.contentLay.removeAllChildren()\n\n    //     this.getTitleNode(this.titleList[0])\n    //     this.tipsList.forEach((title, index) => {\n    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体\n    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)\n    //         infoItemOneController.setData(title)\n    //         this.contentLay.addChild(infoItem)\n    //     })\n\n    //     this.getTitleNode(this.titleList[1])\n    //     this.generationMethodList.forEach((title, index) => {\n    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体\n    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)\n    //         infoItemOneController.setData(title)\n\n    //         let infoImg = cc.instantiate(this.infoImageList[index])\n    //         infoItemOneController.setimgNode(infoImg)\n\n    //         this.contentLay.addChild(infoItem)\n    //     })\n    //     this.getTitleNode(this.titleList[2])\n    //     this.permanentList.forEach((title, index) => {\n    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体\n    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)\n    //         infoItemOneController.setData(title)\n    //         this.contentLay.addChild(infoItem)\n    //     })\n    //     let infoImg1 = cc.instantiate(this.infoImage1)\n    //     this.contentLay.addChild(infoImg1)\n\n\n    //     this.getTitleNode(this.titleList[3])\n    //     this.randomList.forEach((title, index) => {\n    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体\n    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)\n    //         infoItemOneController.setData(title)\n    //         this.contentLay.addChild(infoItem)\n    //     })\n    //     let infoImg2 = cc.instantiate(this.infoImage2)\n    //     this.contentLay.addChild(infoImg2)\n\n    //     this.getTitleNode(this.titleList[4])\n    //     this.chainsList.forEach((title, index) => {\n    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体\n    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)\n    //         infoItemOneController.setData(title)\n    //         this.contentLay.addChild(infoItem)\n    //     })\n    //     this.getTitleNode(this.titleList[5])\n    //     this.iceList.forEach((title, index) => {\n    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体\n    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)\n    //         infoItemOneController.setData(title)\n    //         this.contentLay.addChild(infoItem)\n    //     })\n    //     this.getTitleNode(this.titleList[6])\n    //     this.scoringDetails.forEach((title, index) => {\n    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体\n    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)\n    //         infoItemOneController.setData(title)\n    //         this.contentLay.addChild(infoItem)\n    //     })\n    // }\n\n\n    // getTitleNode(title: string) {\n    //     let infoItem = cc.instantiate(this.infoItem);//初始化一个预制体\n    //     let infoItemController = infoItem.getComponent(InfoItemController)\n    //     infoItemController.setContent(title)\n    //     this.contentLay.addChild(infoItem)\n    // }\n    }\n    \n\n\n    show(backCallback: Function) {\n        this.backCallback = backCallback\n        this.node.active = true\n        this.boardBg.scale = 0\n        // 执行缩放动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { scale: 1 })\n            .start();\n    }\n    hide() {\n        if (this.backCallback) {\n            this.backCallback()\n        }\n        // 执行缩放动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { scale: 0 })\n            .call(() => {\n                this.node.active = false\n            })\n            .start();\n    }\n\n    /**\n     * 初始化规则界面\n     */\n    private initRuleInterface() {\n        // 设置按钮位置\n        if (this.leftButton) {\n            this.leftButton.setPosition(-150, -2);\n        }\n        if (this.rightButton) {\n            this.rightButton.setPosition(142, -2);\n        }\n\n        // 设置按钮点击事件\n        if (this.leftButton) {\n            this.leftButton.on('click', this.onLeftButtonClick, this);\n        }\n        if (this.rightButton) {\n            this.rightButton.on('click', this.onRightButtonClick, this);\n        }\n\n        // 初始化显示状态 - 默认显示单机规则\n        this.switchToRuleType(0);\n    }\n\n    /**\n     * 左边按钮点击事件（单机规则）\n     */\n    private onLeftButtonClick() {\n        cc.log(\"点击单机规则按钮\");\n        this.switchToRuleType(0);\n    }\n\n    /**\n     * 右边按钮点击事件（联机规则）\n     */\n    private onRightButtonClick() {\n        cc.log(\"点击联机规则按钮\");\n        this.switchToRuleType(1);\n    }\n\n    /**\n     * 切换规则类型\n     * @param ruleType 0=单机规则，1=联机规则\n     */\n    private switchToRuleType(ruleType: number) {\n        if (this.currentRuleType === ruleType) {\n            return; // 已经是当前类型，不需要切换\n        }\n\n        cc.log(`切换规则类型: ${this.currentRuleType} -> ${ruleType}`);\n        this.currentRuleType = ruleType;\n\n        // 执行滑动动画切换\n        this.animateRuleSwitch(ruleType);\n    }\n\n    /**\n     * 执行规则切换的滑动动画\n     * @param targetRuleType 目标规则类型\n     */\n    private animateRuleSwitch(targetRuleType: number) {\n        const animationDuration = 0.3; // 动画持续时间\n\n        if (targetRuleType === 0) {\n            // 切换到单机规则\n            cc.log(\"显示单机规则，隐藏联机规则\");\n\n            // 显示单机规则ScrollView\n            if (this.danjiScrollView) {\n                this.danjiScrollView.active = true;\n                // 从右边滑入\n                this.danjiScrollView.x = 300;\n                cc.tween(this.danjiScrollView)\n                    .to(animationDuration, { x: 0 }, { easing: 'quartOut' })\n                    .start();\n            }\n\n            // 隐藏联机规则ScrollView\n            if (this.duorenScrollView) {\n                // 向左滑出\n                cc.tween(this.duorenScrollView)\n                    .to(animationDuration, { x: -300 }, { easing: 'quartOut' })\n                    .call(() => {\n                        this.duorenScrollView.active = false;\n                        this.duorenScrollView.x = 0; // 重置位置\n                    })\n                    .start();\n            }\n        } else {\n            // 切换到联机规则\n            cc.log(\"显示联机规则，隐藏单机规则\");\n\n            // 显示联机规则ScrollView\n            if (this.duorenScrollView) {\n                this.duorenScrollView.active = true;\n                // 从右边滑入\n                this.duorenScrollView.x = 300;\n                cc.tween(this.duorenScrollView)\n                    .to(animationDuration, { x: 0 }, { easing: 'quartOut' })\n                    .start();\n            }\n\n            // 隐藏单机规则ScrollView\n            if (this.danjiScrollView) {\n                // 向左滑出\n                cc.tween(this.danjiScrollView)\n                    .to(animationDuration, { x: -300 }, { easing: 'quartOut' })\n                    .call(() => {\n                        this.danjiScrollView.active = false;\n                        this.danjiScrollView.x = 0; // 重置位置\n                    })\n                    .start();\n            }\n        }\n    }\n\n    // update (dt) {}\n}"]}