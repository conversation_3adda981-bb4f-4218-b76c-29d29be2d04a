{"version": 3, "sources": ["assets/resources/i18n/zh_CN.ts"], "names": [], "mappings": ";;;;;;;AAAa,QAAA,QAAQ,GAAG;IACxB,SAAS;IACL,QAAQ,EAAE,QAAQ;IAClB,SAAS,EAAE,OAAO;IAClB,mBAAmB,EAAE,UAAU;IAC/B,iBAAiB,EAAE,QAAQ;IAC3B,YAAY,EAAE,MAAM;IACpB,UAAU,EAAE,MAAM;IAClB,eAAe,EAAE,OAAO;IACxB,iBAAiB,EAAE,UAAU;IAC7B,gBAAgB,EAAE,OAAO;IACzB,uBAAuB,EAAE,QAAQ;IACjC,eAAe,EAAE,SAAS;IAC1B,WAAW,EAAE,aAAa;IAE1B,gBAAgB,EAAE,QAAQ;IAC1B,sBAAsB,EAAE,QAAQ;IAChC,QAAQ,EAAE,eAAe;IAEzB,MAAM,EAAC,MAAM;IACb,QAAQ,EAAC,MAAM;IACf,SAAS,EAAC,IAAI;IACd,SAAS,EAAC,IAAI;IACd,UAAU,EAAC,MAAM;IACjB,MAAM,EAAC,IAAI;IACX,OAAO,EAAC,IAAI;IACZ,QAAQ,EAAC,IAAI;IACb,IAAI,EAAC,IAAI;IACT,KAAK,EAAC,IAAI;IACV,KAAK,EAAC,IAAI;IACV,KAAK,EAAC,IAAI;IACV,IAAI,EAAC,IAAI;IACT,MAAM,EAAC,IAAI;IACX,IAAI,EAAC,IAAI;IACT,IAAI,EAAC,IAAI;IACT,WAAW,EAAC,KAAK;IACjB,IAAI,EAAC,IAAI;IACT,WAAW,EAAC,MAAM;IAClB,cAAc,EAAC,OAAO;IACtB,UAAU,EAAC,OAAO;IAClB,iBAAiB,EAAC,OAAO;IACzB,IAAI,EAAC,IAAI;IACT,OAAO,EAAC,IAAI;IACZ,MAAM,EAAC,IAAI;IACX,OAAO,EAAC,IAAI;IACZ,KAAK,EAAC,IAAI;IAEV,SAAS,EAAC,KAAK;IAEf,OAAO,EAAC,MAAM;IAEd,IAAI;IACJ,IAAI,EAAE,cAAI;IACV,KAAK,EAAE,0EAAmB;IAC1B,KAAK,EAAE,6NAAyC;IAChD,KAAK,EAAE,wYAAwE;IAC/E,KAAK,EAAE,yOAA2C;IAClD,KAAK,EAAE,4YAAuE;IAE9E,MAAM;IACN,iBAAiB,EAAE,wDAAW;IAC9B,kBAAkB,EAAE,iEAAe;IACnC,kBAAkB,EAAE,2FAAqB;IACzC,kBAAkB,EAAE,mIAA0B;IAC9C,kBAAkB,EAAE,wGAAwB;IAC5C,kBAAkB,EAAE,wGAAwB;IAC5C,kBAAkB,EAAE,iHAAuB;IAC3C,kBAAkB,EAAE,2IAA6B;IACjD,kBAAkB,EAAE,uHAAwB;IAC5C,kBAAkB,EAAE,iHAAuB;IAE3C,MAAM;IACN,cAAc,EAAE,0BAAM;IACtB,eAAe,EAAE,oVAAiE;IAElF,MAAM;IACN,WAAW,EAAE,0BAAM;IACnB,YAAY,EAAE,8QAAkD;IAChE,YAAY,EAAE,6EAAiB;IAC/B,YAAY,EAAE,wIAA0B;IACxC,YAAY,EAAE,qGAAqB;IACnC,YAAY,EAAE,+FAAoB;IAClC,YAAY,EAAE,+FAAoB;IAClC,YAAY,EAAE,yIAA2B;IAEzC,MAAM;IACN,MAAM,EAAE,cAAI;IACZ,OAAO,EAAE,2SAAsD;IAC/D,OAAO,EAAE,yaAA2E;IACpF,OAAO,EAAE,uSAAuD;IAEhE,MAAM;IACN,UAAU,EAAE,cAAI;IAChB,WAAW,EAAE,2SAAsD;IACnE,WAAW,EAAE,qPAA6C;IAC1D,WAAW,EAAE,kSAAuD;IAEpE,MAAM;IACN,eAAe,EAAE,0BAAM;IACvB,gBAAgB,EAAE,oFAAmB;IACrC,gBAAgB,EAAE,wGAAwB;IAC1C,gBAAgB,EAAE,qJAAkC;IACpD,gBAAgB,EAAE,qJAAkC;IACpD,gBAAgB,EAAE,qJAAkC;IACpD,gBAAgB,EAAE,oLAAwC;IAC1D,gBAAgB,EAAE,iNAA4C;IAC9D,gBAAgB,EAAE,iNAA4C;IAC9D,gBAAgB,EAAE,2LAA0C;IAC5D,iBAAiB,EAAE,2LAA0C;IAC7D,iBAAiB,EAAE,2LAA0C;CAChE,CAAC;AAEF,IAAM,KAAK,GAAG,EAAS,CAAC;AACxB,IAAI,CAAC,KAAK,CAAC,QAAQ;IAAE,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;AACzC,KAAK,CAAC,QAAQ,CAAC,KAAK,GAAG,gBAAQ,CAAC", "file": "", "sourceRoot": "/", "sourcesContent": ["export const language = {\n//这部分是通用的\n    kickout1: '您被请出房间',\n    LeaveRoom: '房间已解散',\n    InsufficientBalance: '余额不足，去充值',\n    GameRouteNotFound: '游戏线路异常',\n    NetworkError: '网络异常',\n    RoomIsFull: '房间已满',\n    EnterRoomNumber: '输入房间号',\n    GetUserInfoFailed: '获取用户信息失败',\n    RoomDoesNotExist: '房间不存在',\n    FailedToDeductGoldCoins: '扣除金币失败',\n    ExitApplication: '确定退出游戏？',\n    QuitTheGame: '退出后将无法返回游戏。',\n\n    NotEnoughPlayers: '玩家数量不足',\n    TheGameIsFullOfPlayers: '玩家数量已满',\n    kickout2: '是否将 {0} 请出房间?',//踢出玩家文案\n\n    upSeat:'加入游戏',\n    downSeat:'退出游戏', \n    startGame:'开始', \n    readyGame:'准备', \n    cancelGame:'取消准备', \n    cancel:'取消', \n    confirm:'确定', \n    kickout3:'踢出', \n    back:'返回',//返回\n    leave:'退出',\n    music:'音乐',\n    sound:'音效',\n    join:'加入', //加入\n    create:'创建', //创建\n    auto:'匹配',\n    Room:'房间',\n    room_number:'房间号', //房间号\n    copy:'复制', //复制\n    game_amount:'游戏费用', //游戏费用\n    player_numbers:'玩家数量:', //玩家数量\n    room_exist:'房间不存在',//房间不存在\n    enter_room_number:'输入房间号',//输入房间号\n    free:'免费',\n    players:'玩家', //玩家\n    Player:'玩家',\n    Tickets:'门票',\n    Empty:'空位',\n\n    nextlevel:'下一关',//下一关\n\n    relevel:'再玩一次', //再来一局\n\n    //帮助\n    Tips: `帮助`,\n    Tips1: `1. 每局游戏比赛时间：120秒。`,\n    Tips2: `2. 排名规则：游戏时间结束后，根据玩家的得分高低进行排名，得分相同排名相同。`,\n    Tips3: `3. 消除规则：通过移动游戏中的食物与临近的食物交换位置使相同的食物处于相邻状态，若满足相邻的同样食物>=3个位置交换成功，则完成消除得分。`,\n    Tips4: `4. 道具消除：完成特殊的消除会生成消除道具，道具和附近块交换位置后会使用并消耗。`,\n    Tips5: `5. 连续消除：玩家一次消除行为引发新食物产生，食物下落后触发的消除为连续消除，连续消除期间不可操作。一次下落期间发生的连续消除记为1轮。`,\n\n    //产生方式\n    Generation_Method: `产生方式和使用效果`,\n    Generation_Method1: `1. 消除整列或整行食物。`,\n    Generation_Method2: `2. 消除其中心向外延伸的12个食物。`,\n    Generation_Method3: `3. 消除屏幕中所有与该道具交换位置相同的食物。`,\n    Generation_Method4: `4. 消除1行+1列的食物，包括锁链和冰块。`,\n    Generation_Method5: `5. 消除3行+3列的食物，包括锁链和冰块。`,\n    Generation_Method6: `6. 将屏幕中随机一种食物变为火箭并释放。`,\n    Generation_Method7: `7. 消除其中心向外延伸的24个食物，包括锁链和冰块。`,\n    Generation_Method8: `8. 将屏幕中随机一种食物块变为炸弹并释放。`,\n    Generation_Method9: `9. 消除屏幕中所有食物，包括锁链和冰块。`,\n\n    //常驻任务\n    Permanent_Task: `常驻任务`,\n    Permanent_Task1: `玩家每消除10只螃蟹，便会获得两次随机消除一块2x2区域的奖励，若本次消除螃蟹满足两次及以上，则第二次开始只给一组2x2消除。`,\n\n    //随机任务\n    Random_Task: `随机任务`,\n    Random_Task1: `根据人数增加所需消除的数量，比例为加一人则需多消除一个，例如2人局需消除7次，3人局需消除8次。`,\n    Random_Task2: `- 每次最多同时存在1个任务。`,\n    Random_Task3: `- 任务顺序每局开局随机生成一种，依次交替进行。`,\n    Random_Task4: `- 消除7个指定颜色可获得一个随机彩。`,\n    Random_Task5: `- 消除7个指定颜色可获得一个冰块。`,\n    Random_Task6: `- 消除7个指定颜色可获得一个锁链。`,\n    Random_Task7: `- 消除7个指定颜色可获得一个随机形态的火箭道具。`,\n\n    //锁链简介\n    Chains: `锁链`,\n    Chains1: `1. 消除任务：游戏比赛开始后，每位玩家会收到系统派发的任务，完成任务会向其他玩家释放妨碍道具——锁链。`,\n    Chains2: `2. 锁链的妨碍效果：随机锁住一个位置内的食物。受限制期间，锁链锁住的位置不能移动，锁链内的食物可能会因为其他地块的消除而改变，并非锁住固定食物。`,\n    Chains3: `3. 锁链的解除：在锁链邻近位置（上下左右1格的块）进行普通消除后或者在道具有效区域内，消除1次即可解除。`,\n\n    //冰块简介\n    Ice_Blocks: `冰块`,\n    Ice_Blocks1: `1. 消除任务：游戏比赛开始后，每位玩家会收到系统派发的任务，完成任务会向其他玩家释放妨碍道具——冰块。`,\n    Ice_Blocks2: `2. 冰块的妨碍效果：随机冻住一个食物，受冻期间，位置可以被动移动，玩家不能主动移动。`,\n    Ice_Blocks3: `3. 冰块的解除：在冰块邻近位置（上下左右1格的块）进行普通消除后或者在道具有效区域内,消除1次即可解除。`,\n\n    //得分细则\n    Scoring_Details: `得分细则`,\n    Scoring_Details1: `普通消除：消除单块得分=20 分；`,\n    Scoring_Details2: `连续消除：消除单块得分=连消次数x20 分;`,\n    Scoring_Details3: `火箭消除：火箭激活得分=300分;由火箭消除的单块得分=30分;`,\n    Scoring_Details4: `炸弹消除：炸弹激活得分=500分;由炸弹消除的单块得分=50分;`,\n    Scoring_Details5: `彩虹消除：彩虹激活得分=600分;由彩虹消除的单块得分=60分;`,\n    Scoring_Details6: `火箭组合消除：双火箭激活得分=1200分;由超级火箭消除的单块得分=60分;`,\n    Scoring_Details7: `炸弹火箭组合消除：炸弹火箭组合激活得分=1600分由炸弹火箭消除的单块得分=80分;`,\n    Scoring_Details8: `彩虹火箭组合消除：彩虹火箭组合激活得分=1800分由彩虹火箭消除的单块得分=90分;`,\n    Scoring_Details9: `炸弹组合消除：超级炸弹激活得分=2000分;由超级炸弹消除的单块得分=100分;`,\n    Scoring_Details10: `彩虹炸弹消除：彩虹炸弹激活得分=2200分;由彩虹炸弹消除的单块得分=110分;`,\n    Scoring_Details11: `彩虹组合消除：超级彩虹激活得分=2400分;由超级彩虹消除的单块得分=120分;`,\n};\n\nconst cocos = cc as any;\nif (!cocos.Jou_i18n) cocos.Jou_i18n = {};\ncocos.Jou_i18n.zh_CN = language;"]}