{"version": 3, "sources": ["assets/resources/i18n/en.ts"], "names": [], "mappings": ";;;;;;;AAAa,QAAA,QAAQ,GAAG;IACxB,SAAS;IACL,QAAQ,EAAG,uCAAuC;IAClD,SAAS,EAAG,uBAAuB;IACnC,mBAAmB,EAAG,4DAA4D;IAClF,iBAAiB,EAAG,sBAAsB;IAC1C,YAAY,EAAG,eAAe;IAC9B,UAAU,EAAG,cAAc;IAC3B,eAAe,EAAG,mBAAmB;IACrC,iBAAiB,EAAG,sBAAsB;IAC1C,gBAAgB,EAAG,qBAAqB;IACxC,uBAAuB,EAAG,6BAA6B;IACvD,eAAe,EAAG,wBAAwB;IAC1C,WAAW,EAAG,4DAA4D;IAE1E,gBAAgB,EAAG,oBAAoB;IACvC,sBAAsB,EAAG,6BAA6B;IACtD,QAAQ,EAAE,sCAAsC;IAEhD,MAAM,EAAC,MAAM;IACb,QAAQ,EAAC,OAAO;IAChB,SAAS,EAAC,OAAO;IACjB,SAAS,EAAC,OAAO;IACjB,UAAU,EAAC,QAAQ;IACnB,MAAM,EAAC,QAAQ;IACf,OAAO,EAAC,SAAS;IACjB,QAAQ,EAAC,UAAU;IACnB,IAAI,EAAC,MAAM;IACX,KAAK,EAAC,OAAO;IACb,KAAK,EAAC,OAAO;IACb,KAAK,EAAC,OAAO;IACb,IAAI,EAAC,MAAM;IACX,MAAM,EAAC,QAAQ;IACf,IAAI,EAAC,MAAM;IACX,IAAI,EAAC,MAAM;IACX,WAAW,EAAC,aAAa;IACzB,IAAI,EAAC,MAAM;IACX,WAAW,EAAC,aAAa;IACzB,cAAc,EAAC,iBAAiB;IAChC,UAAU,EAAC,oBAAoB;IAC/B,iBAAiB,EAAC,mBAAmB;IACrC,IAAI,EAAC,MAAM;IACX,OAAO,EAAC,SAAS;IACjB,MAAM,EAAC,QAAQ;IACf,OAAO,EAAC,SAAS;IACjB,KAAK,EAAC,OAAO;IAEb,SAAS,EAAC,MAAM;IAEhB,OAAO,EAAC,YAAY;IAEpB,IAAI;IACJ,IAAI,EAAG,MAAM;IACb,KAAK,EAAG,0CAA0C;IAClD,KAAK,EAAG,6JAA6J;IACrK,KAAK,EAAG,kOAAkO;IAC1O,KAAK,EAAG,wLAAwL;IAChM,KAAK,EAAG,wXAAwX;IAEhY,MAAM;IACN,iBAAiB,EAAG,4BAA4B;IAChD,kBAAkB,EAAG,qDAAqD;IAC1E,kBAAkB,EAAG,+DAA+D;IACpF,kBAAkB,EAAG,gGAAgG;IACrH,kBAAkB,EAAG,uFAAuF;IAC5G,kBAAkB,EAAG,4FAA4F;IACjH,kBAAkB,EAAG,wFAAwF;IAC7G,kBAAkB,EAAG,iGAAiG;IACtH,kBAAkB,EAAG,+EAA+E;IACpG,kBAAkB,EAAG,6EAA6E;IAElG,MAAM;IACN,cAAc,EAAG,gBAAgB;IACjC,eAAe,EAAG,6PAA6P;IAE/Q,MAAM;IACN,WAAW,EAAG,aAAa;IAC3B,YAAY,EAAG,6JAA6J;IAC5K,YAAY,EAAG,sCAAsC;IACrD,YAAY,EAAG,kFAAkF;IACjG,YAAY,EAAG,qEAAgE;IAC/E,YAAY,EAAG,0DAAqD;IACpE,YAAY,EAAG,2DAAsD;IACrE,YAAY,EAAG,0EAAqE;IAEpF,MAAM;IACN,MAAM,EAAG,QAAQ;IACjB,OAAO,EAAG,gMAAsL;IAChM,OAAO,EAAG,mRAAmR;IAC7R,OAAO,EAAG,kPAAkP;IAE5P,MAAM;IACN,UAAU,EAAG,aAAa;IAC1B,WAAW,EAAG,oMAA0L;IACxM,WAAW,EAAG,0KAA0K;IACxL,WAAW,EAAG,4PAA4P;IAE1Q,MAAM;IACN,eAAe,EAAG,iBAAiB;IACnC,gBAAgB,EAAG,6DAA6D;IAChF,gBAAgB,EAAE,uGAAuG;IACzH,gBAAgB,EAAG,6GAA6G;IAChI,gBAAgB,EAAG,uGAAuG;IAC1H,gBAAgB,EAAG,gHAAgH;IACnI,gBAAgB,EAAG,kIAAkI;IACrJ,gBAAgB,EAAG,kJAAkJ;IACrK,gBAAgB,EAAG,2JAA2J;IAC9K,gBAAgB,EAAG,8HAA8H;IACjJ,iBAAiB,EAAG,iIAAiI;IACrJ,iBAAiB,EAAG,uIAAuI;CAC9J,CAAC;AAEF,IAAM,KAAK,GAAG,EAAS,CAAC;AACxB,IAAI,CAAC,KAAK,CAAC,QAAQ;IAAE,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;AACzC,KAAK,CAAC,QAAQ,CAAC,EAAE,GAAG,gBAAQ,CAAC", "file": "", "sourceRoot": "/", "sourcesContent": ["export const language = {\n//这部分是通用的\n    kickout1 : 'You have been asked to leave the room',//您被请出房间\n    LeaveRoom : 'The room is dissolved',//房间已解散\n    InsufficientBalance : 'The current balance is insufficient, please go to purchase',//余额不足\n    GameRouteNotFound : 'Game route not found',//游戏线路异常\n    NetworkError : 'network error',//网络异常\n    RoomIsFull : 'Room is full',//房间已满\n    EnterRoomNumber : 'Enter room number',//输入房间号\n    GetUserInfoFailed : 'get user info failed',//获取用户信息失败\n    RoomDoesNotExist : 'Room does not exist',//房间不存在\n    FailedToDeductGoldCoins : 'Failed to deduct gold coins',//扣除金币失败\n    ExitApplication : 'Are you sure to leave?',//完全退出游戏\n    QuitTheGame : 'Once you exit the game, you won’t be able to return to it.',//退出本局游戏\n\n    NotEnoughPlayers : 'Not enough players',//玩家数量不足\n    TheGameIsFullOfPlayers : 'The game is full of players',//玩家数量已满\n    kickout2: 'Whether to kick {0} out of the room?',//踢出玩家文案\n\n    upSeat:'Join', //上座\n    downSeat:'Leave', //下座\n    startGame:'Start', //开始游戏\n    readyGame:'Ready', //准备\n    cancelGame:'Cancel', //取消准备\n    cancel:'Cancel', \n    confirm:'Confirm', \n    kickout3:'Kick Out', \n    back:'Back',//返回\n    leave:'Leave', //退出\n    music:'Music',  //音乐\n    sound:'Sound', //音效\n    join:'Join', //加入\n    create:'Create', //创建\n    auto:'Auto',\n    Room:'Room',\n    room_number:'Room Number', //房间号\n    copy:'Copy', //复制\n    game_amount:'Game Amount', //游戏费用\n    player_numbers:'Player Numbers:', //玩家数量\n    room_exist:'Room doesn’t exist',//房间不存在\n    enter_room_number:'Enter room number',//输入房间号\n    free:'Free',\n    players:'Players', //玩家\n    Player:'Player',\n    Tickets:'Tickets',\n    Empty:'Empty',\n\n    nextlevel:'Next',//下一关\n\n    relevel:'Play Again', //再来一局\n\n    //帮助\n    Tips : `Tips`,\n    Tips1 : `1. Game Duration per Round: 120 seconds `,\n    Tips2 : `2. Ranking Rules: Upon the conclusion of the game time, players are ranked based on their scores. Players with the same score will share the same ranking. `,\n    Tips3 : `3. Elimination Rule: By swapping adjacent food items in the game, players can align identical food items. If 3 or more identical food items are adjacent after the swap, the elimination is successful, and points are awarded. `,\n    Tips4 : `4. Item Elimination: Completing special eliminations will generate elimination items. These items will be used and consumed when swapped with adjacent blocks. Details are as follows:`,\n    Tips5 : `5.Combo Elimination: When a player's elimination action triggers the generation of new food items, and the subsequent falling of these items leads to further eliminations, this is considered a combo elimination. During combo eliminations, players cannot perform any actions. Each sequence of eliminations caused by a single round of falling food items is counted as 1 round.`,\n    \n    //产生方式\n    Generation_Method : `Generation Method & Effect`,\n    Generation_Method1 : `1. Eliminate an entire row or column of food items.`,\n    Generation_Method2 : `2. Eliminate 12 food items extending outward from its center.`,\n    Generation_Method3 : `3. Eliminate all food items on the screen that are the same as the one swapped with the item. `,\n    Generation_Method4 : `4. Eliminate one row and one column of food items, including chains and ice blocks.  `,\n    Generation_Method5 : `5. Eliminate three rows and three columns of food items, including chains and ice blocks. `,\n    Generation_Method6 : `6. Transform a random type of food item on the screen into a rocket and activate it.  `,\n    Generation_Method7 : `7. Eliminate 24 food items extending outward from its center, including chains and ice blocks. `,\n    Generation_Method8 : `8. Transform a random food block on the screen into a bomb and activate it.  `,\n    Generation_Method9 : `9. Eliminate all food items on the screen, including chains and ice blocks.`,\n\n    //常驻任务\n    Permanent_Task : `Permanent Task`,\n    Permanent_Task1 : `Every time a player eliminates 6 crabs, they will receive a reward of randomly eliminating a 2x2 area twice. If the current crab elimination satisfies the condition twice or more, only one 2x2 elimination will be granted starting from the second time.`,\n\n    //随机任务\n    Random_Task : `Random Task`,\n    Random_Task1 : `The required elimination count scales with player count: +1 elimination per additional player (e.g. 2 players require 7 eliminations, 3 players require 8).`,\n    Random_Task2 : `- Maximum 1 active mission at a time`,\n    Random_Task3 : `- Mission sequence is randomly generated at match start and executed in sequence`,\n    Random_Task4 : `- Eliminate 7xdesignated colors → Spawn 1 random special candy`,\n    Random_Task5 : `- Eliminate 7xdesignated colors → Spawn 1 ice block`,\n    Random_Task6 : `- Eliminate 7xdesignated colors → Spawn 1 chain lock`,\n    Random_Task7 : `- Eliminate 7xdesignated colors → Spawn 1 random-shaped rocket item`,\n\n    //锁链简介\n    Chains : `Chains`,\n    Chains1 : `1. Elimination Task: After the game starts, each player will receive a task assigned by the system. Completing the task will release a hindrance item—**chains**—to other players.  `,\n    Chains2 : `2. Hindrance Effect of Chains: Randomly locks a food item in a specific position. During the restriction period, the locked position cannot be moved. The food item within the chain may change due to eliminations in other areas, meaning it does not lock a fixed food item.  `,\n    Chains3 : `3. Chain Removal: Chains can be removed by performing a normal elimination in an adjacent position (blocks up, down, left, or right within 1 grid) or within the effective area of an item. One elimination is sufficient to remove the chain.  `,\n\n    //冰块简介\n    Ice_Blocks : `Ice Blocks:`,\n    Ice_Blocks1 : `1. Elimination Task: After the game starts, each player will receive a task assigned by the system. Completing the task will release a hindrance item—**ice blocks**—to other players.  `,\n    Ice_Blocks2 : `2. Hindrance Effect of Ice Blocks: Randomly freezes a food item. During the freezing period, the position can be passively moved, but players cannot actively move it.  `,\n    Ice_Blocks3 : `3. Ice Block Removal: Ice blocks can be removed by performing a normal elimination in an adjacent position (blocks up, down, left, or right within 1 grid) or within the effective area of an item. One elimination is sufficient to remove the ice block.`,\n\n    //得分细则\n    Scoring_Details : `Scoring Details`,\n    Scoring_Details1 : `Normal Elimination: Score per block eliminated : 20 points;`,\n    Scoring_Details2: `Consecutive Elimination: Score per block eliminated : Number of consecutive eliminations x 20 points;`,\n    Scoring_Details3 : `Rocket Elimination: Rocket activation score : 300 points; Score per block eliminated by rocket : 30 points;`,\n    Scoring_Details4 : `Bomb Elimination: Bomb activation score : 500 points; Score per block eliminated by bomb : 50 points;`,\n    Scoring_Details5 : `Rainbow Elimination: Rainbow activation score : 600 points; Score per block eliminated by rainbow : 60 points;`,\n    Scoring_Details6 : `Combined Rocket Elimination: Dual rocket activation score : 1200 points; Score per block eliminated by super rocket : 60 points;`,\n    Scoring_Details7 : `Bomb-Rocket Combined Elimination: Bomb-rocket combination activation score : 1600 points; Score per block eliminated by bomb-rocket : 80 points;`,\n    Scoring_Details8 : `Rainbow-Rocket Combined Elimination: Rainbow-rocket combination activation score : 1800 points; Score per block eliminated by rainbow-rocket : 90 points;`,\n    Scoring_Details9 : `Combined Bomb Elimination: Super bomb activation score : 2000 points; Score per block eliminated by super bomb : 100 points;`,\n    Scoring_Details10 : `Rainbow Bomb Elimination: Rainbow bomb activation score : 2200 points; Score per block eliminated by rainbow bomb : 110 points;`,\n    Scoring_Details11 : `Combined Rainbow Elimination: Super rainbow activation score : 2400 points; Score per block eliminated by super rainbow : 120 points;`,\n};\n\nconst cocos = cc as any;\nif (!cocos.Jou_i18n) cocos.Jou_i18n = {};\ncocos.Jou_i18n.en = language;"]}