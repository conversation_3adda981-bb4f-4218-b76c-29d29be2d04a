
(function () {
var scripts = [{"deps":{"./joui18n-script/LocalizedSprite":1,"./joui18n-script/LocalizedLabel":2,"./assets/meshTools/tools/Publish":3,"./assets/scripts/bean/GameBean":4,"./assets/scripts/game/Chess/HexChessBoardController":5,"./assets/scripts/hall/Level/LevelSelectController":6,"./assets/resources/i18n/zh_HK":7,"./assets/meshTools/BaseSDK":8,"./assets/scripts/common/GameData":9,"./assets/scripts/level/LevelPageController":10,"./assets/scripts/net/GameServerUrl":11,"./assets/scripts/util/BlockingQueue":12,"./assets/scripts/start_up/StartUpPageController":13,"./assets/scripts/TipsDialogController":14,"./assets/scripts/test/NoticeRoundStartTest":15,"./assets/meshTools/tools/MeshSdk":16,"./assets/meshTools/Singleton":17,"./assets/scripts/pfb/InfoItemOneController":18,"./assets/scripts/GlobalManagerController":19,"./assets/scripts/bean/GlobalBean":20,"./assets/scripts/common/GameMgr":21,"./assets/scripts/common/GameTools":22,"./assets/scripts/bean/LanguageType":23,"./assets/scripts/game/CongratsDialogController":24,"./assets/scripts/game/GameScoreController":25,"./assets/scripts/game/BtnController":26,"./assets/scripts/game/Chess/GridController":27,"./assets/meshTools/tools/MeshSdkApi":28,"./assets/scripts/hall/HallCenterLayController":29,"./assets/scripts/common/EventCenter":30,"./assets/scripts/game/GamePageController":31,"./assets/scripts/game/Chess/ChessBoardController":32,"./assets/scripts/hall/HallPageController":33,"./assets/scripts/hall/HallJoinRoomController":34,"./assets/scripts/hall/HallCreateRoomController":35,"./assets/scripts/common/MineConsole":36,"./assets/scripts/hall/HallParentController":37,"./assets/scripts/hall/InfoDialogController":38,"./assets/scripts/hall/LevelSelectDemo":39,"./assets/scripts/hall/PlayerLayoutController":40,"./assets/scripts/hall/SettingDialogController":41,"./assets/scripts/hall/MatchParentController":42,"./assets/scripts/bean/EnumBean":43,"./assets/scripts/hall/Level/LevelSelectExample":44,"./assets/scripts/hall/Level/LevelSelectPageController":45,"./assets/scripts/hall/HallAutoController":46,"./assets/scripts/hall/Level/LevelItemController":47,"./assets/scripts/hall/LeaveDialogController":48,"./assets/scripts/hall/Level/ScrollViewHelper":49,"./assets/scripts/net/IHttpMsgBody":50,"./assets/scripts/net/HttpManager":51,"./assets/scripts/hall/Level/LevelSelectDemoInLevel":52,"./assets/scripts/ToastController":53,"./assets/scripts/net/MessageBaseBean":54,"./assets/scripts/net/MessageId":55,"./assets/scripts/net/WebSocketManager":56,"./assets/scripts/pfb/InfoItemController":57,"./assets/scripts/net/WebSocketTool":58,"./assets/scripts/net/HttpUtils":59,"./assets/scripts/net/ErrorCode":60,"./assets/scripts/hall/KickOutDialogController":61,"./assets/scripts/pfb/SeatItemController":62,"./assets/scripts/start_up/StartUpCenterController":63,"./assets/scripts/pfb/PlayerGameController ":64,"./assets/scripts/pfb/CongratsItemController":65,"./assets/scripts/hall/TopUpDialogController":66,"./assets/scripts/util/NickNameLabel":67,"./assets/scripts/util/Dictionary":68,"./assets/scripts/util/LocalStorageManager":69,"./assets/scripts/pfb/MatchItemController":70,"./assets/resources/i18n/en":71,"./assets/scripts/util/AudioManager":72,"./assets/scripts/util/Tools":73,"./assets/meshTools/MeshTools":74,"./assets/scripts/util/AudioMgr":75,"./assets/scripts/util/Config":76,"./assets/scripts/pfb/PlayerScoreController":77,"./assets/resources/i18n/zh_CN":78},"path":"preview-scripts/__qc_index__.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedSprite.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedLabel.js"},{"deps":{"../Singleton":17},"path":"preview-scripts/assets/meshTools/tools/Publish.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/GameBean.js"},{"deps":{"../../bean/GlobalBean":20,"../../pfb/PlayerGameController ":64},"path":"preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js"},{"deps":{"./ScrollViewHelper":49},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectController.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_HK.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/BaseSDK.js"},{"deps":{"../../meshTools/MeshTools":74,"../../meshTools/Singleton":17,"../net/GameServerUrl":11},"path":"preview-scripts/assets/scripts/common/GameData.js"},{"deps":{"../net/MessageId":55,"../net/WebSocketManager":56,"../hall/LeaveDialogController":48,"../util/Tools":73,"../util/Config":76},"path":"preview-scripts/assets/scripts/level/LevelPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/GameServerUrl.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/BlockingQueue.js"},{"deps":{"../common/GameMgr":21,"./StartUpCenterController":63},"path":"preview-scripts/assets/scripts/start_up/StartUpPageController.js"},{"deps":{"./util/Config":76,"./util/Tools":73},"path":"preview-scripts/assets/scripts/TipsDialogController.js"},{"deps":{"../common/GameMgr":21,"../common/EventCenter":30,"../net/MessageId":55},"path":"preview-scripts/assets/scripts/test/NoticeRoundStartTest.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/tools/MeshSdk.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/Singleton.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemOneController.js"},{"deps":{"../meshTools/MeshTools":74,"../meshTools/tools/Publish":3,"./bean/EnumBean":43,"./bean/GlobalBean":20,"./bean/LanguageType":23,"./common/EventCenter":30,"./common/GameMgr":21,"./game/GamePageController":31,"./hall/HallPageController":33,"./level/LevelPageController":10,"./hall/TopUpDialogController":66,"./net/ErrorCode":60,"./net/GameServerUrl":11,"./net/MessageBaseBean":54,"./net/MessageId":55,"./net/WebSocketManager":56,"./net/WebSocketTool":58,"./start_up/StartUpPageController":13,"./TipsDialogController":14,"./ToastController":53,"./util/AudioMgr":75,"./util/Config":76},"path":"preview-scripts/assets/scripts/GlobalManagerController.js"},{"deps":{"../../meshTools/Singleton":17,"../hall/HallAutoController":46},"path":"preview-scripts/assets/scripts/bean/GlobalBean.js"},{"deps":{"../../meshTools/tools/MeshSdkApi":28,"./EventCenter":30,"./GameData":9,"./GameTools":22,"./MineConsole":36},"path":"preview-scripts/assets/scripts/common/GameMgr.js"},{"deps":{"../../meshTools/Singleton":17},"path":"preview-scripts/assets/scripts/common/GameTools.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/LanguageType.js"},{"deps":{"../bean/GlobalBean":20,"../common/EventCenter":30,"../common/GameMgr":21,"../net/MessageBaseBean":54,"../pfb/CongratsItemController":65,"../util/Config":76,"../util/Tools":73},"path":"preview-scripts/assets/scripts/game/CongratsDialogController.js"},{"deps":{"../bean/GlobalBean":20,"../pfb/PlayerScoreController":77},"path":"preview-scripts/assets/scripts/game/GameScoreController.js"},{"deps":{"../util/AudioManager":72,"../util/Config":76,"../util/LocalStorageManager":69},"path":"preview-scripts/assets/scripts/game/BtnController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/Chess/GridController.js"},{"deps":{"../MeshTools":74,"../BaseSDK":8,"../../scripts/net/MessageBaseBean":54,"../../scripts/common/GameMgr":21,"../../scripts/common/EventCenter":30,"MeshSdk":16},"path":"preview-scripts/assets/meshTools/tools/MeshSdkApi.js"},{"deps":{"../bean/GlobalBean":20,"../net/MessageId":55,"../net/WebSocketManager":56,"../ToastController":53,"./HallAutoController":46,"./HallCreateRoomController":35,"./HallJoinRoomController":34},"path":"preview-scripts/assets/scripts/hall/HallCenterLayController.js"},{"deps":{"../../meshTools/Singleton":17,"./GameMgr":21},"path":"preview-scripts/assets/scripts/common/EventCenter.js"},{"deps":{"../bean/GlobalBean":20,"../hall/LeaveDialogController":48,"../util/AudioManager":72,"../util/Config":76,"../util/Tools":73,"./CongratsDialogController":24,"./GameScoreController":25,"./Chess/ChessBoardController":32,"./Chess/HexChessBoardController":5,"../net/WebSocketManager":56,"../net/MessageId":55},"path":"preview-scripts/assets/scripts/game/GamePageController.js"},{"deps":{"../../bean/GlobalBean":20,"../../pfb/PlayerGameController ":64},"path":"preview-scripts/assets/scripts/game/Chess/ChessBoardController.js"},{"deps":{"../bean/GlobalBean":20,"../common/GameMgr":21,"../net/MessageId":55,"../net/WebSocketManager":56,"../net/WebSocketTool":58,"../ToastController":53,"../util/AudioManager":72,"./HallParentController":37,"./InfoDialogController":38,"./KickOutDialogController":61,"./LeaveDialogController":48,"./Level/LevelSelectPageController":45,"./MatchParentController":42,"./SettingDialogController":41},"path":"preview-scripts/assets/scripts/hall/HallPageController.js"},{"deps":{"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/HallJoinRoomController.js"},{"deps":{"../bean/GlobalBean":20,"../pfb/SeatItemController":62,"../util/Config":76,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/HallCreateRoomController.js"},{"deps":{"../../meshTools/Singleton":17},"path":"preview-scripts/assets/scripts/common/MineConsole.js"},{"deps":{"../../meshTools/tools/Publish":3,"../bean/GlobalBean":20,"../common/GameMgr":21,"../net/MessageId":55,"../net/WebSocketManager":56,"../ToastController":53,"../util/Config":76,"../util/Tools":73,"./HallCenterLayController":29},"path":"preview-scripts/assets/scripts/hall/HallParentController.js"},{"deps":{"../util/Config":76,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/InfoDialogController.js"},{"deps":{"./Level/LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/LevelSelectDemo.js"},{"deps":{"../bean/GlobalBean":20,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/PlayerLayoutController.js"},{"deps":{"../../meshTools/tools/Publish":3,"../util/AudioManager":72,"../util/Config":76,"../util/LocalStorageManager":69,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/SettingDialogController.js"},{"deps":{"../../meshTools/tools/Publish":3,"../bean/GlobalBean":20,"../common/EventCenter":30,"../common/GameMgr":21,"../net/MessageBaseBean":54,"../pfb/MatchItemController":70,"../util/Config":76,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/MatchParentController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/EnumBean.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js"},{"deps":{"../../GlobalManagerController":19,"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js"},{"deps":{"../bean/GlobalBean":20,"../util/Config":76,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/HallAutoController.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelItemController.js"},{"deps":{"../common/GameMgr":21,"../net/MessageId":55,"../net/WebSocketManager":56,"../util/Config":76,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/LeaveDialogController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/hall/Level/ScrollViewHelper.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/IHttpMsgBody.js"},{"deps":{"./HttpUtils":59,"./MessageBaseBean":54,"./GameServerUrl":11,"../../meshTools/MeshTools":74,"../common/GameMgr":21,"../common/EventCenter":30},"path":"preview-scripts/assets/scripts/net/HttpManager.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectDemoInLevel.js"},{"deps":{},"path":"preview-scripts/assets/scripts/ToastController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageBaseBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageId.js"},{"deps":{"../../meshTools/Singleton":17,"../common/EventCenter":30,"../common/GameMgr":21,"./WebSocketTool":58},"path":"preview-scripts/assets/scripts/net/WebSocketManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemController.js"},{"deps":{"./MessageBaseBean":54,"./MessageId":55,"../util/Tools":73,"../../meshTools/Singleton":17,"../common/EventCenter":30,"../common/GameMgr":21},"path":"preview-scripts/assets/scripts/net/WebSocketTool.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/HttpUtils.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/ErrorCode.js"},{"deps":{"../net/MessageId":55,"../net/WebSocketManager":56,"../util/Config":76,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/KickOutDialogController.js"},{"deps":{"../util/NickNameLabel":67,"../util/Tools":73},"path":"preview-scripts/assets/scripts/pfb/SeatItemController.js"},{"deps":{"../common/EventCenter":30,"../common/GameMgr":21,"../net/MessageBaseBean":54,"../util/Config":76},"path":"preview-scripts/assets/scripts/start_up/StartUpCenterController.js"},{"deps":{"../util/Tools":73},"path":"preview-scripts/assets/scripts/pfb/PlayerGameController .js"},{"deps":{"../../meshTools/tools/Publish":3,"../util/Config":76,"../util/NickNameLabel":67,"../util/Tools":73},"path":"preview-scripts/assets/scripts/pfb/CongratsItemController.js"},{"deps":{"../common/GameMgr":21,"../util/Config":76,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/TopUpDialogController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/NickNameLabel.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Dictionary.js"},{"deps":{"../../meshTools/Singleton":17},"path":"preview-scripts/assets/scripts/util/LocalStorageManager.js"},{"deps":{"../util/NickNameLabel":67,"../util/Tools":73},"path":"preview-scripts/assets/scripts/pfb/MatchItemController.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/en.js"},{"deps":{"./AudioMgr":75,"./LocalStorageManager":69},"path":"preview-scripts/assets/scripts/util/AudioManager.js"},{"deps":{"./AudioManager":72,"./Config":76},"path":"preview-scripts/assets/scripts/util/Tools.js"},{"deps":{"./tools/Publish":3},"path":"preview-scripts/assets/meshTools/MeshTools.js"},{"deps":{"./Config":76,"./Dictionary":68},"path":"preview-scripts/assets/scripts/util/AudioMgr.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Config.js"},{"deps":{"../bean/GlobalBean":20,"../util/NickNameLabel":67,"../util/Tools":73},"path":"preview-scripts/assets/scripts/pfb/PlayerScoreController.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_CN.js"}];
var entries = ["preview-scripts/__qc_index__.js"];
var bundleScript = 'preview-scripts/__qc_bundle__.js';

/**
 * Notice: This file can not use ES6 (for IE 11)
 */
var modules = {};
var name2path = {};

// Will generated by module.js plugin
// var scripts = ${scripts};
// var entries = ${entries};
// var bundleScript = ${bundleScript};

if (typeof global === 'undefined') {
    window.global = window;
}

var isJSB = typeof jsb !== 'undefined';

function getXMLHttpRequest () {
    return window.XMLHttpRequest ? new window.XMLHttpRequest() : new ActiveXObject('MSXML2.XMLHTTP');
}

function downloadText(url, callback) {
    if (isJSB) {
        var result = jsb.fileUtils.getStringFromFile(url);
        callback(null, result);
        return;
    }

    var xhr = getXMLHttpRequest(),
        errInfo = 'Load text file failed: ' + url;
    xhr.open('GET', url, true);
    if (xhr.overrideMimeType) xhr.overrideMimeType('text\/plain; charset=utf-8');
    xhr.onload = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200 || xhr.status === 0) {
                callback(null, xhr.responseText);
            }
            else {
                callback({status:xhr.status, errorMessage:errInfo + ', status: ' + xhr.status});
            }
        }
        else {
            callback({status:xhr.status, errorMessage:errInfo + '(wrong readyState)'});
        }
    };
    xhr.onerror = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(error)'});
    };
    xhr.ontimeout = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(time out)'});
    };
    xhr.send(null);
};

function loadScript (src, cb) {
    if (typeof require !== 'undefined') {
        require(src);
        return cb();
    }

    // var timer = 'load ' + src;
    // console.time(timer);

    var scriptElement = document.createElement('script');

    function done() {
        // console.timeEnd(timer);
        // deallocation immediate whatever
        scriptElement.remove();
    }

    scriptElement.onload = function () {
        done();
        cb();
    };
    scriptElement.onerror = function () {
        done();
        var error = 'Failed to load ' + src;
        console.error(error);
        cb(new Error(error));
    };
    scriptElement.setAttribute('type','text/javascript');
    scriptElement.setAttribute('charset', 'utf-8');
    scriptElement.setAttribute('src', src);

    document.head.appendChild(scriptElement);
}

function loadScripts (srcs, cb) {
    var n = srcs.length;

    srcs.forEach(function (src) {
        loadScript(src, function () {
            n--;
            if (n === 0) {
                cb();
            }
        });
    })
}

function formatPath (path) {
    let destPath = window.__quick_compile_project__.destPath;
    if (destPath) {
        let prefix = 'preview-scripts';
        if (destPath[destPath.length - 1] === '/') {
            prefix += '/';
        }
        path = path.replace(prefix, destPath);
    }
    return path;
}

window.__quick_compile_project__ = {
    destPath: '',

    registerModule: function (path, module) {
        path = formatPath(path);
        modules[path].module = module;
    },

    registerModuleFunc: function (path, func) {
        path = formatPath(path);
        modules[path].func = func;

        var sections = path.split('/');
        var name = sections[sections.length - 1];
        name = name.replace(/\.(?:js|ts|json)$/i, '');
        name2path[name] = path;
    },

    require: function (request, path) {
        var m, requestScript;

        path = formatPath(path);
        if (path) {
            m = modules[path];
            if (!m) {
                console.warn('Can not find module for path : ' + path);
                return null;
            }
        }

        if (m) {
            let depIndex = m.deps[request];
            // dependence script was excluded
            if (depIndex === -1) {
                return null;
            }
            else {
                requestScript = scripts[ m.deps[request] ];
            }
        }
        
        let requestPath = '';
        if (!requestScript) {
            // search from name2path when request is a dynamic module name
            if (/^[\w- .]*$/.test(request)) {
                requestPath = name2path[request];
            }

            if (!requestPath) {
                if (CC_JSB) {
                    return require(request);
                }
                else {
                    console.warn('Can not find deps [' + request + '] for path : ' + path);
                    return null;
                }
            }
        }
        else {
            requestPath = formatPath(requestScript.path);
        }

        let requestModule = modules[requestPath];
        if (!requestModule) {
            console.warn('Can not find request module for path : ' + requestPath);
            return null;
        }

        if (!requestModule.module && requestModule.func) {
            requestModule.func();
        }

        if (!requestModule.module) {
            console.warn('Can not find requestModule.module for path : ' + path);
            return null;
        }

        return requestModule.module.exports;
    },

    run: function () {
        entries.forEach(function (entry) {
            entry = formatPath(entry);
            var module = modules[entry];
            if (!module.module) {
                module.func();
            }
        });
    },

    load: function (cb) {
        var self = this;

        var srcs = scripts.map(function (script) {
            var path = formatPath(script.path);
            modules[path] = script;

            if (script.mtime) {
                path += ("?mtime=" + script.mtime);
            }
            return path;
        });

        console.time && console.time('load __quick_compile_project__');
        // jsb can not analysis sourcemap, so keep separate files.
        if (bundleScript && !isJSB) {
            downloadText(formatPath(bundleScript), function (err, bundleSource) {
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                if (err) {
                    console.error(err);
                    return;
                }

                let evalTime = 'eval __quick_compile_project__ : ' + srcs.length + ' files';
                console.time && console.time(evalTime);
                var sources = bundleSource.split('\n//------QC-SOURCE-SPLIT------\n');
                for (var i = 0; i < sources.length; i++) {
                    if (sources[i]) {
                        window.eval(sources[i]);
                        // not sure why new Function cannot set breakpoints precisely
                        // new Function(sources[i])()
                    }
                }
                self.run();
                console.timeEnd && console.timeEnd(evalTime);
                cb();
            })
        }
        else {
            loadScripts(srcs, function () {
                self.run();
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                cb();
            });
        }
    }
};

// Polyfill for IE 11
if (!('remove' in Element.prototype)) {
    Element.prototype.remove = function () {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}
})();
    