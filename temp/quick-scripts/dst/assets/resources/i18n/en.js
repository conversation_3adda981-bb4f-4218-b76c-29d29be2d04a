
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/resources/i18n/en.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'cc55fhCTRdMjZxuQuVowyEX', 'en');
// resources/i18n/en.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.language = void 0;
exports.language = {
    //这部分是通用的
    kickout1: 'You have been asked to leave the room',
    LeaveRoom: 'The room is dissolved',
    InsufficientBalance: 'The current balance is insufficient, please go to purchase',
    GameRouteNotFound: 'Game route not found',
    NetworkError: 'network error',
    RoomIsFull: 'Room is full',
    EnterRoomNumber: 'Enter room number',
    GetUserInfoFailed: 'get user info failed',
    RoomDoesNotExist: 'Room does not exist',
    FailedToDeductGoldCoins: 'Failed to deduct gold coins',
    ExitApplication: 'Are you sure to leave?',
    QuitTheGame: 'Once you exit the game, you won’t be able to return to it.',
    NotEnoughPlayers: 'Not enough players',
    TheGameIsFullOfPlayers: 'The game is full of players',
    kickout2: 'Whether to kick {0} out of the room?',
    upSeat: 'Join',
    downSeat: 'Leave',
    startGame: 'Start',
    readyGame: 'Ready',
    cancelGame: 'Cancel',
    cancel: 'Cancel',
    confirm: 'Confirm',
    kickout3: 'Kick Out',
    back: 'Back',
    leave: 'Leave',
    music: 'Music',
    sound: 'Sound',
    join: 'Join',
    create: 'Create',
    auto: 'Auto',
    Room: 'Room',
    room_number: 'Room Number',
    copy: 'Copy',
    game_amount: 'Game Amount',
    player_numbers: 'Player Numbers:',
    room_exist: 'Room doesn’t exist',
    enter_room_number: 'Enter room number',
    free: 'Free',
    players: 'Players',
    Player: 'Player',
    Tickets: 'Tickets',
    Empty: 'Empty',
    nextlevel: 'Next',
    relevel: 'Play Again',
    //帮助
    Tips: "Tips",
    Tips1: "1. Game Duration per Round: 120 seconds ",
    Tips2: "2. Ranking Rules: Upon the conclusion of the game time, players are ranked based on their scores. Players with the same score will share the same ranking. ",
    Tips3: "3. Elimination Rule: By swapping adjacent food items in the game, players can align identical food items. If 3 or more identical food items are adjacent after the swap, the elimination is successful, and points are awarded. ",
    Tips4: "4. Item Elimination: Completing special eliminations will generate elimination items. These items will be used and consumed when swapped with adjacent blocks. Details are as follows:",
    Tips5: "5.Combo Elimination: When a player's elimination action triggers the generation of new food items, and the subsequent falling of these items leads to further eliminations, this is considered a combo elimination. During combo eliminations, players cannot perform any actions. Each sequence of eliminations caused by a single round of falling food items is counted as 1 round.",
    //产生方式
    Generation_Method: "Generation Method & Effect",
    Generation_Method1: "1. Eliminate an entire row or column of food items.",
    Generation_Method2: "2. Eliminate 12 food items extending outward from its center.",
    Generation_Method3: "3. Eliminate all food items on the screen that are the same as the one swapped with the item. ",
    Generation_Method4: "4. Eliminate one row and one column of food items, including chains and ice blocks.  ",
    Generation_Method5: "5. Eliminate three rows and three columns of food items, including chains and ice blocks. ",
    Generation_Method6: "6. Transform a random type of food item on the screen into a rocket and activate it.  ",
    Generation_Method7: "7. Eliminate 24 food items extending outward from its center, including chains and ice blocks. ",
    Generation_Method8: "8. Transform a random food block on the screen into a bomb and activate it.  ",
    Generation_Method9: "9. Eliminate all food items on the screen, including chains and ice blocks.",
    //常驻任务
    Permanent_Task: "Permanent Task",
    Permanent_Task1: "Every time a player eliminates 6 crabs, they will receive a reward of randomly eliminating a 2x2 area twice. If the current crab elimination satisfies the condition twice or more, only one 2x2 elimination will be granted starting from the second time.",
    //随机任务
    Random_Task: "Random Task",
    Random_Task1: "The required elimination count scales with player count: +1 elimination per additional player (e.g. 2 players require 7 eliminations, 3 players require 8).",
    Random_Task2: "- Maximum 1 active mission at a time",
    Random_Task3: "- Mission sequence is randomly generated at match start and executed in sequence",
    Random_Task4: "- Eliminate 7xdesignated colors \u2192 Spawn 1 random special candy",
    Random_Task5: "- Eliminate 7xdesignated colors \u2192 Spawn 1 ice block",
    Random_Task6: "- Eliminate 7xdesignated colors \u2192 Spawn 1 chain lock",
    Random_Task7: "- Eliminate 7xdesignated colors \u2192 Spawn 1 random-shaped rocket item",
    //锁链简介
    Chains: "Chains",
    Chains1: "1. Elimination Task: After the game starts, each player will receive a task assigned by the system. Completing the task will release a hindrance item\u2014**chains**\u2014to other players.  ",
    Chains2: "2. Hindrance Effect of Chains: Randomly locks a food item in a specific position. During the restriction period, the locked position cannot be moved. The food item within the chain may change due to eliminations in other areas, meaning it does not lock a fixed food item.  ",
    Chains3: "3. Chain Removal: Chains can be removed by performing a normal elimination in an adjacent position (blocks up, down, left, or right within 1 grid) or within the effective area of an item. One elimination is sufficient to remove the chain.  ",
    //冰块简介
    Ice_Blocks: "Ice Blocks:",
    Ice_Blocks1: "1. Elimination Task: After the game starts, each player will receive a task assigned by the system. Completing the task will release a hindrance item\u2014**ice blocks**\u2014to other players.  ",
    Ice_Blocks2: "2. Hindrance Effect of Ice Blocks: Randomly freezes a food item. During the freezing period, the position can be passively moved, but players cannot actively move it.  ",
    Ice_Blocks3: "3. Ice Block Removal: Ice blocks can be removed by performing a normal elimination in an adjacent position (blocks up, down, left, or right within 1 grid) or within the effective area of an item. One elimination is sufficient to remove the ice block.",
    //得分细则
    Scoring_Details: "Scoring Details",
    Scoring_Details1: "Normal Elimination: Score per block eliminated : 20 points;",
    Scoring_Details2: "Consecutive Elimination: Score per block eliminated : Number of consecutive eliminations x 20 points;",
    Scoring_Details3: "Rocket Elimination: Rocket activation score : 300 points; Score per block eliminated by rocket : 30 points;",
    Scoring_Details4: "Bomb Elimination: Bomb activation score : 500 points; Score per block eliminated by bomb : 50 points;",
    Scoring_Details5: "Rainbow Elimination: Rainbow activation score : 600 points; Score per block eliminated by rainbow : 60 points;",
    Scoring_Details6: "Combined Rocket Elimination: Dual rocket activation score : 1200 points; Score per block eliminated by super rocket : 60 points;",
    Scoring_Details7: "Bomb-Rocket Combined Elimination: Bomb-rocket combination activation score : 1600 points; Score per block eliminated by bomb-rocket : 80 points;",
    Scoring_Details8: "Rainbow-Rocket Combined Elimination: Rainbow-rocket combination activation score : 1800 points; Score per block eliminated by rainbow-rocket : 90 points;",
    Scoring_Details9: "Combined Bomb Elimination: Super bomb activation score : 2000 points; Score per block eliminated by super bomb : 100 points;",
    Scoring_Details10: "Rainbow Bomb Elimination: Rainbow bomb activation score : 2200 points; Score per block eliminated by rainbow bomb : 110 points;",
    Scoring_Details11: "Combined Rainbow Elimination: Super rainbow activation score : 2400 points; Score per block eliminated by super rainbow : 120 points;",
};
var cocos = cc;
if (!cocos.Jou_i18n)
    cocos.Jou_i18n = {};
cocos.Jou_i18n.en = exports.language;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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