
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/resources/i18n/zh_CN.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '83ee5xvZ1VBLb3UezEVIqou', 'zh_CN');
// resources/i18n/zh_CN.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.language = void 0;
exports.language = {
    //这部分是通用的
    kickout1: '您被请出房间',
    LeaveRoom: '房间已解散',
    InsufficientBalance: '余额不足，去充值',
    GameRouteNotFound: '游戏线路异常',
    NetworkError: '网络异常',
    RoomIsFull: '房间已满',
    EnterRoomNumber: '输入房间号',
    GetUserInfoFailed: '获取用户信息失败',
    RoomDoesNotExist: '房间不存在',
    FailedToDeductGoldCoins: '扣除金币失败',
    ExitApplication: '确定退出游戏？',
    QuitTheGame: '退出后将无法返回游戏。',
    NotEnoughPlayers: '玩家数量不足',
    TheGameIsFullOfPlayers: '玩家数量已满',
    kickout2: '是否将 {0} 请出房间?',
    upSeat: '加入游戏',
    downSeat: '退出游戏',
    startGame: '开始',
    readyGame: '准备',
    cancelGame: '取消准备',
    cancel: '取消',
    confirm: '确定',
    kickout3: '踢出',
    back: '返回',
    leave: '退出',
    music: '音乐',
    sound: '音效',
    join: '加入',
    create: '创建',
    auto: '匹配',
    Room: '房间',
    room_number: '房间号',
    copy: '复制',
    game_amount: '游戏费用',
    player_numbers: '玩家数量:',
    room_exist: '房间不存在',
    enter_room_number: '输入房间号',
    free: '免费',
    players: '玩家',
    Player: '玩家',
    Tickets: '门票',
    Empty: '空位',
    nextlevel: '下一关',
    relevel: '再玩一次',
    //帮助
    Tips: "\u5E2E\u52A9",
    Tips1: "1. \u6BCF\u5C40\u6E38\u620F\u6BD4\u8D5B\u65F6\u95F4\uFF1A120\u79D2\u3002",
    Tips2: "2. \u6392\u540D\u89C4\u5219\uFF1A\u6E38\u620F\u65F6\u95F4\u7ED3\u675F\u540E\uFF0C\u6839\u636E\u73A9\u5BB6\u7684\u5F97\u5206\u9AD8\u4F4E\u8FDB\u884C\u6392\u540D\uFF0C\u5F97\u5206\u76F8\u540C\u6392\u540D\u76F8\u540C\u3002",
    Tips3: "3. \u6D88\u9664\u89C4\u5219\uFF1A\u901A\u8FC7\u79FB\u52A8\u6E38\u620F\u4E2D\u7684\u98DF\u7269\u4E0E\u4E34\u8FD1\u7684\u98DF\u7269\u4EA4\u6362\u4F4D\u7F6E\u4F7F\u76F8\u540C\u7684\u98DF\u7269\u5904\u4E8E\u76F8\u90BB\u72B6\u6001\uFF0C\u82E5\u6EE1\u8DB3\u76F8\u90BB\u7684\u540C\u6837\u98DF\u7269>=3\u4E2A\u4F4D\u7F6E\u4EA4\u6362\u6210\u529F\uFF0C\u5219\u5B8C\u6210\u6D88\u9664\u5F97\u5206\u3002",
    Tips4: "4. \u9053\u5177\u6D88\u9664\uFF1A\u5B8C\u6210\u7279\u6B8A\u7684\u6D88\u9664\u4F1A\u751F\u6210\u6D88\u9664\u9053\u5177\uFF0C\u9053\u5177\u548C\u9644\u8FD1\u5757\u4EA4\u6362\u4F4D\u7F6E\u540E\u4F1A\u4F7F\u7528\u5E76\u6D88\u8017\u3002",
    Tips5: "5. \u8FDE\u7EED\u6D88\u9664\uFF1A\u73A9\u5BB6\u4E00\u6B21\u6D88\u9664\u884C\u4E3A\u5F15\u53D1\u65B0\u98DF\u7269\u4EA7\u751F\uFF0C\u98DF\u7269\u4E0B\u843D\u540E\u89E6\u53D1\u7684\u6D88\u9664\u4E3A\u8FDE\u7EED\u6D88\u9664\uFF0C\u8FDE\u7EED\u6D88\u9664\u671F\u95F4\u4E0D\u53EF\u64CD\u4F5C\u3002\u4E00\u6B21\u4E0B\u843D\u671F\u95F4\u53D1\u751F\u7684\u8FDE\u7EED\u6D88\u9664\u8BB0\u4E3A1\u8F6E\u3002",
    //产生方式
    Generation_Method: "\u4EA7\u751F\u65B9\u5F0F\u548C\u4F7F\u7528\u6548\u679C",
    Generation_Method1: "1. \u6D88\u9664\u6574\u5217\u6216\u6574\u884C\u98DF\u7269\u3002",
    Generation_Method2: "2. \u6D88\u9664\u5176\u4E2D\u5FC3\u5411\u5916\u5EF6\u4F38\u768412\u4E2A\u98DF\u7269\u3002",
    Generation_Method3: "3. \u6D88\u9664\u5C4F\u5E55\u4E2D\u6240\u6709\u4E0E\u8BE5\u9053\u5177\u4EA4\u6362\u4F4D\u7F6E\u76F8\u540C\u7684\u98DF\u7269\u3002",
    Generation_Method4: "4. \u6D88\u96641\u884C+1\u5217\u7684\u98DF\u7269\uFF0C\u5305\u62EC\u9501\u94FE\u548C\u51B0\u5757\u3002",
    Generation_Method5: "5. \u6D88\u96643\u884C+3\u5217\u7684\u98DF\u7269\uFF0C\u5305\u62EC\u9501\u94FE\u548C\u51B0\u5757\u3002",
    Generation_Method6: "6. \u5C06\u5C4F\u5E55\u4E2D\u968F\u673A\u4E00\u79CD\u98DF\u7269\u53D8\u4E3A\u706B\u7BAD\u5E76\u91CA\u653E\u3002",
    Generation_Method7: "7. \u6D88\u9664\u5176\u4E2D\u5FC3\u5411\u5916\u5EF6\u4F38\u768424\u4E2A\u98DF\u7269\uFF0C\u5305\u62EC\u9501\u94FE\u548C\u51B0\u5757\u3002",
    Generation_Method8: "8. \u5C06\u5C4F\u5E55\u4E2D\u968F\u673A\u4E00\u79CD\u98DF\u7269\u5757\u53D8\u4E3A\u70B8\u5F39\u5E76\u91CA\u653E\u3002",
    Generation_Method9: "9. \u6D88\u9664\u5C4F\u5E55\u4E2D\u6240\u6709\u98DF\u7269\uFF0C\u5305\u62EC\u9501\u94FE\u548C\u51B0\u5757\u3002",
    //常驻任务
    Permanent_Task: "\u5E38\u9A7B\u4EFB\u52A1",
    Permanent_Task1: "\u73A9\u5BB6\u6BCF\u6D88\u966410\u53EA\u8783\u87F9\uFF0C\u4FBF\u4F1A\u83B7\u5F97\u4E24\u6B21\u968F\u673A\u6D88\u9664\u4E00\u57572x2\u533A\u57DF\u7684\u5956\u52B1\uFF0C\u82E5\u672C\u6B21\u6D88\u9664\u8783\u87F9\u6EE1\u8DB3\u4E24\u6B21\u53CA\u4EE5\u4E0A\uFF0C\u5219\u7B2C\u4E8C\u6B21\u5F00\u59CB\u53EA\u7ED9\u4E00\u7EC42x2\u6D88\u9664\u3002",
    //随机任务
    Random_Task: "\u968F\u673A\u4EFB\u52A1",
    Random_Task1: "\u6839\u636E\u4EBA\u6570\u589E\u52A0\u6240\u9700\u6D88\u9664\u7684\u6570\u91CF\uFF0C\u6BD4\u4F8B\u4E3A\u52A0\u4E00\u4EBA\u5219\u9700\u591A\u6D88\u9664\u4E00\u4E2A\uFF0C\u4F8B\u59822\u4EBA\u5C40\u9700\u6D88\u96647\u6B21\uFF0C3\u4EBA\u5C40\u9700\u6D88\u96648\u6B21\u3002",
    Random_Task2: "- \u6BCF\u6B21\u6700\u591A\u540C\u65F6\u5B58\u57281\u4E2A\u4EFB\u52A1\u3002",
    Random_Task3: "- \u4EFB\u52A1\u987A\u5E8F\u6BCF\u5C40\u5F00\u5C40\u968F\u673A\u751F\u6210\u4E00\u79CD\uFF0C\u4F9D\u6B21\u4EA4\u66FF\u8FDB\u884C\u3002",
    Random_Task4: "- \u6D88\u96647\u4E2A\u6307\u5B9A\u989C\u8272\u53EF\u83B7\u5F97\u4E00\u4E2A\u968F\u673A\u5F69\u3002",
    Random_Task5: "- \u6D88\u96647\u4E2A\u6307\u5B9A\u989C\u8272\u53EF\u83B7\u5F97\u4E00\u4E2A\u51B0\u5757\u3002",
    Random_Task6: "- \u6D88\u96647\u4E2A\u6307\u5B9A\u989C\u8272\u53EF\u83B7\u5F97\u4E00\u4E2A\u9501\u94FE\u3002",
    Random_Task7: "- \u6D88\u96647\u4E2A\u6307\u5B9A\u989C\u8272\u53EF\u83B7\u5F97\u4E00\u4E2A\u968F\u673A\u5F62\u6001\u7684\u706B\u7BAD\u9053\u5177\u3002",
    //锁链简介
    Chains: "\u9501\u94FE",
    Chains1: "1. \u6D88\u9664\u4EFB\u52A1\uFF1A\u6E38\u620F\u6BD4\u8D5B\u5F00\u59CB\u540E\uFF0C\u6BCF\u4F4D\u73A9\u5BB6\u4F1A\u6536\u5230\u7CFB\u7EDF\u6D3E\u53D1\u7684\u4EFB\u52A1\uFF0C\u5B8C\u6210\u4EFB\u52A1\u4F1A\u5411\u5176\u4ED6\u73A9\u5BB6\u91CA\u653E\u59A8\u788D\u9053\u5177\u2014\u2014\u9501\u94FE\u3002",
    Chains2: "2. \u9501\u94FE\u7684\u59A8\u788D\u6548\u679C\uFF1A\u968F\u673A\u9501\u4F4F\u4E00\u4E2A\u4F4D\u7F6E\u5185\u7684\u98DF\u7269\u3002\u53D7\u9650\u5236\u671F\u95F4\uFF0C\u9501\u94FE\u9501\u4F4F\u7684\u4F4D\u7F6E\u4E0D\u80FD\u79FB\u52A8\uFF0C\u9501\u94FE\u5185\u7684\u98DF\u7269\u53EF\u80FD\u4F1A\u56E0\u4E3A\u5176\u4ED6\u5730\u5757\u7684\u6D88\u9664\u800C\u6539\u53D8\uFF0C\u5E76\u975E\u9501\u4F4F\u56FA\u5B9A\u98DF\u7269\u3002",
    Chains3: "3. \u9501\u94FE\u7684\u89E3\u9664\uFF1A\u5728\u9501\u94FE\u90BB\u8FD1\u4F4D\u7F6E\uFF08\u4E0A\u4E0B\u5DE6\u53F31\u683C\u7684\u5757\uFF09\u8FDB\u884C\u666E\u901A\u6D88\u9664\u540E\u6216\u8005\u5728\u9053\u5177\u6709\u6548\u533A\u57DF\u5185\uFF0C\u6D88\u96641\u6B21\u5373\u53EF\u89E3\u9664\u3002",
    //冰块简介
    Ice_Blocks: "\u51B0\u5757",
    Ice_Blocks1: "1. \u6D88\u9664\u4EFB\u52A1\uFF1A\u6E38\u620F\u6BD4\u8D5B\u5F00\u59CB\u540E\uFF0C\u6BCF\u4F4D\u73A9\u5BB6\u4F1A\u6536\u5230\u7CFB\u7EDF\u6D3E\u53D1\u7684\u4EFB\u52A1\uFF0C\u5B8C\u6210\u4EFB\u52A1\u4F1A\u5411\u5176\u4ED6\u73A9\u5BB6\u91CA\u653E\u59A8\u788D\u9053\u5177\u2014\u2014\u51B0\u5757\u3002",
    Ice_Blocks2: "2. \u51B0\u5757\u7684\u59A8\u788D\u6548\u679C\uFF1A\u968F\u673A\u51BB\u4F4F\u4E00\u4E2A\u98DF\u7269\uFF0C\u53D7\u51BB\u671F\u95F4\uFF0C\u4F4D\u7F6E\u53EF\u4EE5\u88AB\u52A8\u79FB\u52A8\uFF0C\u73A9\u5BB6\u4E0D\u80FD\u4E3B\u52A8\u79FB\u52A8\u3002",
    Ice_Blocks3: "3. \u51B0\u5757\u7684\u89E3\u9664\uFF1A\u5728\u51B0\u5757\u90BB\u8FD1\u4F4D\u7F6E\uFF08\u4E0A\u4E0B\u5DE6\u53F31\u683C\u7684\u5757\uFF09\u8FDB\u884C\u666E\u901A\u6D88\u9664\u540E\u6216\u8005\u5728\u9053\u5177\u6709\u6548\u533A\u57DF\u5185,\u6D88\u96641\u6B21\u5373\u53EF\u89E3\u9664\u3002",
    //得分细则
    Scoring_Details: "\u5F97\u5206\u7EC6\u5219",
    Scoring_Details1: "\u666E\u901A\u6D88\u9664\uFF1A\u6D88\u9664\u5355\u5757\u5F97\u5206=20 \u5206\uFF1B",
    Scoring_Details2: "\u8FDE\u7EED\u6D88\u9664\uFF1A\u6D88\u9664\u5355\u5757\u5F97\u5206=\u8FDE\u6D88\u6B21\u6570x20 \u5206;",
    Scoring_Details3: "\u706B\u7BAD\u6D88\u9664\uFF1A\u706B\u7BAD\u6FC0\u6D3B\u5F97\u5206=300\u5206;\u7531\u706B\u7BAD\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=30\u5206;",
    Scoring_Details4: "\u70B8\u5F39\u6D88\u9664\uFF1A\u70B8\u5F39\u6FC0\u6D3B\u5F97\u5206=500\u5206;\u7531\u70B8\u5F39\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=50\u5206;",
    Scoring_Details5: "\u5F69\u8679\u6D88\u9664\uFF1A\u5F69\u8679\u6FC0\u6D3B\u5F97\u5206=600\u5206;\u7531\u5F69\u8679\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=60\u5206;",
    Scoring_Details6: "\u706B\u7BAD\u7EC4\u5408\u6D88\u9664\uFF1A\u53CC\u706B\u7BAD\u6FC0\u6D3B\u5F97\u5206=1200\u5206;\u7531\u8D85\u7EA7\u706B\u7BAD\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=60\u5206;",
    Scoring_Details7: "\u70B8\u5F39\u706B\u7BAD\u7EC4\u5408\u6D88\u9664\uFF1A\u70B8\u5F39\u706B\u7BAD\u7EC4\u5408\u6FC0\u6D3B\u5F97\u5206=1600\u5206\u7531\u70B8\u5F39\u706B\u7BAD\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=80\u5206;",
    Scoring_Details8: "\u5F69\u8679\u706B\u7BAD\u7EC4\u5408\u6D88\u9664\uFF1A\u5F69\u8679\u706B\u7BAD\u7EC4\u5408\u6FC0\u6D3B\u5F97\u5206=1800\u5206\u7531\u5F69\u8679\u706B\u7BAD\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=90\u5206;",
    Scoring_Details9: "\u70B8\u5F39\u7EC4\u5408\u6D88\u9664\uFF1A\u8D85\u7EA7\u70B8\u5F39\u6FC0\u6D3B\u5F97\u5206=2000\u5206;\u7531\u8D85\u7EA7\u70B8\u5F39\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=100\u5206;",
    Scoring_Details10: "\u5F69\u8679\u70B8\u5F39\u6D88\u9664\uFF1A\u5F69\u8679\u70B8\u5F39\u6FC0\u6D3B\u5F97\u5206=2200\u5206;\u7531\u5F69\u8679\u70B8\u5F39\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=110\u5206;",
    Scoring_Details11: "\u5F69\u8679\u7EC4\u5408\u6D88\u9664\uFF1A\u8D85\u7EA7\u5F69\u8679\u6FC0\u6D3B\u5F97\u5206=2400\u5206;\u7531\u8D85\u7EA7\u5F69\u8679\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=120\u5206;",
};
var cocos = cc;
if (!cocos.Jou_i18n)
    cocos.Jou_i18n = {};
cocos.Jou_i18n.zh_CN = exports.language;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9yZXNvdXJjZXMvaTE4bi96aF9DTi50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBYSxRQUFBLFFBQVEsR0FBRztJQUN4QixTQUFTO0lBQ0wsUUFBUSxFQUFFLFFBQVE7SUFDbEIsU0FBUyxFQUFFLE9BQU87SUFDbEIsbUJBQW1CLEVBQUUsVUFBVTtJQUMvQixpQkFBaUIsRUFBRSxRQUFRO0lBQzNCLFlBQVksRUFBRSxNQUFNO0lBQ3BCLFVBQVUsRUFBRSxNQUFNO0lBQ2xCLGVBQWUsRUFBRSxPQUFPO0lBQ3hCLGlCQUFpQixFQUFFLFVBQVU7SUFDN0IsZ0JBQWdCLEVBQUUsT0FBTztJQUN6Qix1QkFBdUIsRUFBRSxRQUFRO0lBQ2pDLGVBQWUsRUFBRSxTQUFTO0lBQzFCLFdBQVcsRUFBRSxhQUFhO0lBRTFCLGdCQUFnQixFQUFFLFFBQVE7SUFDMUIsc0JBQXNCLEVBQUUsUUFBUTtJQUNoQyxRQUFRLEVBQUUsZUFBZTtJQUV6QixNQUFNLEVBQUMsTUFBTTtJQUNiLFFBQVEsRUFBQyxNQUFNO0lBQ2YsU0FBUyxFQUFDLElBQUk7SUFDZCxTQUFTLEVBQUMsSUFBSTtJQUNkLFVBQVUsRUFBQyxNQUFNO0lBQ2pCLE1BQU0sRUFBQyxJQUFJO0lBQ1gsT0FBTyxFQUFDLElBQUk7SUFDWixRQUFRLEVBQUMsSUFBSTtJQUNiLElBQUksRUFBQyxJQUFJO0lBQ1QsS0FBSyxFQUFDLElBQUk7SUFDVixLQUFLLEVBQUMsSUFBSTtJQUNWLEtBQUssRUFBQyxJQUFJO0lBQ1YsSUFBSSxFQUFDLElBQUk7SUFDVCxNQUFNLEVBQUMsSUFBSTtJQUNYLElBQUksRUFBQyxJQUFJO0lBQ1QsSUFBSSxFQUFDLElBQUk7SUFDVCxXQUFXLEVBQUMsS0FBSztJQUNqQixJQUFJLEVBQUMsSUFBSTtJQUNULFdBQVcsRUFBQyxNQUFNO0lBQ2xCLGNBQWMsRUFBQyxPQUFPO0lBQ3RCLFVBQVUsRUFBQyxPQUFPO0lBQ2xCLGlCQUFpQixFQUFDLE9BQU87SUFDekIsSUFBSSxFQUFDLElBQUk7SUFDVCxPQUFPLEVBQUMsSUFBSTtJQUNaLE1BQU0sRUFBQyxJQUFJO0lBQ1gsT0FBTyxFQUFDLElBQUk7SUFDWixLQUFLLEVBQUMsSUFBSTtJQUVWLFNBQVMsRUFBQyxLQUFLO0lBRWYsT0FBTyxFQUFDLE1BQU07SUFFZCxJQUFJO0lBQ0osSUFBSSxFQUFFLGNBQUk7SUFDVixLQUFLLEVBQUUsMEVBQW1CO0lBQzFCLEtBQUssRUFBRSw2TkFBeUM7SUFDaEQsS0FBSyxFQUFFLHdZQUF3RTtJQUMvRSxLQUFLLEVBQUUseU9BQTJDO0lBQ2xELEtBQUssRUFBRSw0WUFBdUU7SUFFOUUsTUFBTTtJQUNOLGlCQUFpQixFQUFFLHdEQUFXO0lBQzlCLGtCQUFrQixFQUFFLGlFQUFlO0lBQ25DLGtCQUFrQixFQUFFLDJGQUFxQjtJQUN6QyxrQkFBa0IsRUFBRSxtSUFBMEI7SUFDOUMsa0JBQWtCLEVBQUUsd0dBQXdCO0lBQzVDLGtCQUFrQixFQUFFLHdHQUF3QjtJQUM1QyxrQkFBa0IsRUFBRSxpSEFBdUI7SUFDM0Msa0JBQWtCLEVBQUUsMklBQTZCO0lBQ2pELGtCQUFrQixFQUFFLHVIQUF3QjtJQUM1QyxrQkFBa0IsRUFBRSxpSEFBdUI7SUFFM0MsTUFBTTtJQUNOLGNBQWMsRUFBRSwwQkFBTTtJQUN0QixlQUFlLEVBQUUsb1ZBQWlFO0lBRWxGLE1BQU07SUFDTixXQUFXLEVBQUUsMEJBQU07SUFDbkIsWUFBWSxFQUFFLDhRQUFrRDtJQUNoRSxZQUFZLEVBQUUsNkVBQWlCO0lBQy9CLFlBQVksRUFBRSx3SUFBMEI7SUFDeEMsWUFBWSxFQUFFLHFHQUFxQjtJQUNuQyxZQUFZLEVBQUUsK0ZBQW9CO0lBQ2xDLFlBQVksRUFBRSwrRkFBb0I7SUFDbEMsWUFBWSxFQUFFLHlJQUEyQjtJQUV6QyxNQUFNO0lBQ04sTUFBTSxFQUFFLGNBQUk7SUFDWixPQUFPLEVBQUUsMlNBQXNEO0lBQy9ELE9BQU8sRUFBRSx5YUFBMkU7SUFDcEYsT0FBTyxFQUFFLHVTQUF1RDtJQUVoRSxNQUFNO0lBQ04sVUFBVSxFQUFFLGNBQUk7SUFDaEIsV0FBVyxFQUFFLDJTQUFzRDtJQUNuRSxXQUFXLEVBQUUscVBBQTZDO0lBQzFELFdBQVcsRUFBRSxrU0FBdUQ7SUFFcEUsTUFBTTtJQUNOLGVBQWUsRUFBRSwwQkFBTTtJQUN2QixnQkFBZ0IsRUFBRSxvRkFBbUI7SUFDckMsZ0JBQWdCLEVBQUUsd0dBQXdCO0lBQzFDLGdCQUFnQixFQUFFLHFKQUFrQztJQUNwRCxnQkFBZ0IsRUFBRSxxSkFBa0M7SUFDcEQsZ0JBQWdCLEVBQUUscUpBQWtDO0lBQ3BELGdCQUFnQixFQUFFLG9MQUF3QztJQUMxRCxnQkFBZ0IsRUFBRSxpTkFBNEM7SUFDOUQsZ0JBQWdCLEVBQUUsaU5BQTRDO0lBQzlELGdCQUFnQixFQUFFLDJMQUEwQztJQUM1RCxpQkFBaUIsRUFBRSwyTEFBMEM7SUFDN0QsaUJBQWlCLEVBQUUsMkxBQTBDO0NBQ2hFLENBQUM7QUFFRixJQUFNLEtBQUssR0FBRyxFQUFTLENBQUM7QUFDeEIsSUFBSSxDQUFDLEtBQUssQ0FBQyxRQUFRO0lBQUUsS0FBSyxDQUFDLFFBQVEsR0FBRyxFQUFFLENBQUM7QUFDekMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxLQUFLLEdBQUcsZ0JBQVEsQ0FBQyIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBsYW5ndWFnZSA9IHtcbi8v6L+Z6YOo5YiG5piv6YCa55So55qEXG4gICAga2lja291dDE6ICfmgqjooqvor7flh7rmiL/pl7QnLFxuICAgIExlYXZlUm9vbTogJ+aIv+mXtOW3suino+aVoycsXG4gICAgSW5zdWZmaWNpZW50QmFsYW5jZTogJ+S9memineS4jei2s++8jOWOu+WFheWAvCcsXG4gICAgR2FtZVJvdXRlTm90Rm91bmQ6ICfmuLjmiI/nur/ot6/lvILluLgnLFxuICAgIE5ldHdvcmtFcnJvcjogJ+e9kee7nOW8guW4uCcsXG4gICAgUm9vbUlzRnVsbDogJ+aIv+mXtOW3sua7oScsXG4gICAgRW50ZXJSb29tTnVtYmVyOiAn6L6T5YWl5oi/6Ze05Y+3JyxcbiAgICBHZXRVc2VySW5mb0ZhaWxlZDogJ+iOt+WPlueUqOaIt+S/oeaBr+Wksei0pScsXG4gICAgUm9vbURvZXNOb3RFeGlzdDogJ+aIv+mXtOS4jeWtmOWcqCcsXG4gICAgRmFpbGVkVG9EZWR1Y3RHb2xkQ29pbnM6ICfmiaPpmaTph5HluIHlpLHotKUnLFxuICAgIEV4aXRBcHBsaWNhdGlvbjogJ+ehruWumumAgOWHuua4uOaIj++8nycsXG4gICAgUXVpdFRoZUdhbWU6ICfpgIDlh7rlkI7lsIbml6Dms5Xov5Tlm57muLjmiI/jgIInLFxuXG4gICAgTm90RW5vdWdoUGxheWVyczogJ+eOqeWutuaVsOmHj+S4jei2sycsXG4gICAgVGhlR2FtZUlzRnVsbE9mUGxheWVyczogJ+eOqeWutuaVsOmHj+W3sua7oScsXG4gICAga2lja291dDI6ICfmmK/lkKblsIYgezB9IOivt+WHuuaIv+mXtD8nLC8v6Lii5Ye6546p5a625paH5qGIXG5cbiAgICB1cFNlYXQ6J+WKoOWFpea4uOaIjycsXG4gICAgZG93blNlYXQ6J+mAgOWHuua4uOaIjycsIFxuICAgIHN0YXJ0R2FtZTon5byA5aeLJywgXG4gICAgcmVhZHlHYW1lOiflh4blpIcnLCBcbiAgICBjYW5jZWxHYW1lOiflj5bmtojlh4blpIcnLCBcbiAgICBjYW5jZWw6J+WPlua2iCcsIFxuICAgIGNvbmZpcm06J+ehruWumicsIFxuICAgIGtpY2tvdXQzOifouKLlh7onLCBcbiAgICBiYWNrOifov5Tlm54nLC8v6L+U5ZueXG4gICAgbGVhdmU6J+mAgOWHuicsXG4gICAgbXVzaWM6J+mfs+S5kCcsXG4gICAgc291bmQ6J+mfs+aViCcsXG4gICAgam9pbjon5Yqg5YWlJywgLy/liqDlhaVcbiAgICBjcmVhdGU6J+WIm+W7uicsIC8v5Yib5bu6XG4gICAgYXV0bzon5Yy56YWNJyxcbiAgICBSb29tOifmiL/pl7QnLFxuICAgIHJvb21fbnVtYmVyOifmiL/pl7Tlj7cnLCAvL+aIv+mXtOWPt1xuICAgIGNvcHk6J+WkjeWIticsIC8v5aSN5Yi2XG4gICAgZ2FtZV9hbW91bnQ6J+a4uOaIj+i0ueeUqCcsIC8v5ri45oiP6LS555SoXG4gICAgcGxheWVyX251bWJlcnM6J+eOqeWutuaVsOmHjzonLCAvL+eOqeWutuaVsOmHj1xuICAgIHJvb21fZXhpc3Q6J+aIv+mXtOS4jeWtmOWcqCcsLy/miL/pl7TkuI3lrZjlnKhcbiAgICBlbnRlcl9yb29tX251bWJlcjon6L6T5YWl5oi/6Ze05Y+3JywvL+i+k+WFpeaIv+mXtOWPt1xuICAgIGZyZWU6J+WFjei0uScsXG4gICAgcGxheWVyczon546p5a62JywgLy/njqnlrrZcbiAgICBQbGF5ZXI6J+eOqeWuticsXG4gICAgVGlja2V0czon6Zeo56WoJyxcbiAgICBFbXB0eTon56m65L2NJyxcblxuICAgIG5leHRsZXZlbDon5LiL5LiA5YWzJywvL+S4i+S4gOWFs1xuXG4gICAgcmVsZXZlbDon5YaN546p5LiA5qyhJywgLy/lho3mnaXkuIDlsYBcblxuICAgIC8v5biu5YqpXG4gICAgVGlwczogYOW4ruWKqWAsXG4gICAgVGlwczE6IGAxLiDmr4/lsYDmuLjmiI/mr5TotZvml7bpl7TvvJoxMjDnp5LjgIJgLFxuICAgIFRpcHMyOiBgMi4g5o6S5ZCN6KeE5YiZ77ya5ri45oiP5pe26Ze057uT5p2f5ZCO77yM5qC55o2u546p5a6255qE5b6X5YiG6auY5L2O6L+b6KGM5o6S5ZCN77yM5b6X5YiG55u45ZCM5o6S5ZCN55u45ZCM44CCYCxcbiAgICBUaXBzMzogYDMuIOa2iOmZpOinhOWIme+8mumAmui/h+enu+WKqOa4uOaIj+S4reeahOmjn+eJqeS4juS4tOi/keeahOmjn+eJqeS6pOaNouS9jee9ruS9v+ebuOWQjOeahOmjn+eJqeWkhOS6juebuOmCu+eKtuaAge+8jOiLpea7oei2s+ebuOmCu+eahOWQjOagt+mjn+eJqT49M+S4quS9jee9ruS6pOaNouaIkOWKn++8jOWImeWujOaIkOa2iOmZpOW+l+WIhuOAgmAsXG4gICAgVGlwczQ6IGA0LiDpgZPlhbfmtojpmaTvvJrlrozmiJDnibnmrornmoTmtojpmaTkvJrnlJ/miJDmtojpmaTpgZPlhbfvvIzpgZPlhbflkozpmYTov5HlnZfkuqTmjaLkvY3nva7lkI7kvJrkvb/nlKjlubbmtojogJfjgIJgLFxuICAgIFRpcHM1OiBgNS4g6L+e57ut5raI6Zmk77ya546p5a625LiA5qyh5raI6Zmk6KGM5Li65byV5Y+R5paw6aOf54mp5Lqn55Sf77yM6aOf54mp5LiL6JC95ZCO6Kem5Y+R55qE5raI6Zmk5Li66L+e57ut5raI6Zmk77yM6L+e57ut5raI6Zmk5pyf6Ze05LiN5Y+v5pON5L2c44CC5LiA5qyh5LiL6JC95pyf6Ze05Y+R55Sf55qE6L+e57ut5raI6Zmk6K6w5Li6Mei9ruOAgmAsXG5cbiAgICAvL+S6p+eUn+aWueW8j1xuICAgIEdlbmVyYXRpb25fTWV0aG9kOiBg5Lqn55Sf5pa55byP5ZKM5L2/55So5pWI5p6cYCxcbiAgICBHZW5lcmF0aW9uX01ldGhvZDE6IGAxLiDmtojpmaTmlbTliJfmiJbmlbTooYzpo5/nianjgIJgLFxuICAgIEdlbmVyYXRpb25fTWV0aG9kMjogYDIuIOa2iOmZpOWFtuS4reW/g+WQkeWkluW7tuS8uOeahDEy5Liq6aOf54mp44CCYCxcbiAgICBHZW5lcmF0aW9uX01ldGhvZDM6IGAzLiDmtojpmaTlsY/luZXkuK3miYDmnInkuI7or6XpgZPlhbfkuqTmjaLkvY3nva7nm7jlkIznmoTpo5/nianjgIJgLFxuICAgIEdlbmVyYXRpb25fTWV0aG9kNDogYDQuIOa2iOmZpDHooYwrMeWIl+eahOmjn+eJqe+8jOWMheaLrOmUgemTvuWSjOWGsOWdl+OAgmAsXG4gICAgR2VuZXJhdGlvbl9NZXRob2Q1OiBgNS4g5raI6ZmkM+ihjCsz5YiX55qE6aOf54mp77yM5YyF5ous6ZSB6ZO+5ZKM5Yaw5Z2X44CCYCxcbiAgICBHZW5lcmF0aW9uX01ldGhvZDY6IGA2LiDlsIblsY/luZXkuK3pmo/mnLrkuIDnp43po5/nianlj5jkuLrngavnrq3lubbph4rmlL7jgIJgLFxuICAgIEdlbmVyYXRpb25fTWV0aG9kNzogYDcuIOa2iOmZpOWFtuS4reW/g+WQkeWkluW7tuS8uOeahDI05Liq6aOf54mp77yM5YyF5ous6ZSB6ZO+5ZKM5Yaw5Z2X44CCYCxcbiAgICBHZW5lcmF0aW9uX01ldGhvZDg6IGA4LiDlsIblsY/luZXkuK3pmo/mnLrkuIDnp43po5/nianlnZflj5jkuLrngrjlvLnlubbph4rmlL7jgIJgLFxuICAgIEdlbmVyYXRpb25fTWV0aG9kOTogYDkuIOa2iOmZpOWxj+W5leS4reaJgOaciemjn+eJqe+8jOWMheaLrOmUgemTvuWSjOWGsOWdl+OAgmAsXG5cbiAgICAvL+W4uOmpu+S7u+WKoVxuICAgIFBlcm1hbmVudF9UYXNrOiBg5bi46am75Lu75YqhYCxcbiAgICBQZXJtYW5lbnRfVGFzazE6IGDnjqnlrrbmr4/mtojpmaQxMOWPquieg+ifue+8jOS+v+S8muiOt+W+l+S4pOasoemaj+acuua2iOmZpOS4gOWdlzJ4MuWMuuWfn+eahOWlluWKse+8jOiLpeacrOasoea2iOmZpOieg+ifuea7oei2s+S4pOasoeWPiuS7peS4iu+8jOWImeesrOS6jOasoeW8gOWni+WPque7meS4gOe7hDJ4Mua2iOmZpOOAgmAsXG5cbiAgICAvL+maj+acuuS7u+WKoVxuICAgIFJhbmRvbV9UYXNrOiBg6ZqP5py65Lu75YqhYCxcbiAgICBSYW5kb21fVGFzazE6IGDmoLnmja7kurrmlbDlop7liqDmiYDpnIDmtojpmaTnmoTmlbDph4/vvIzmr5TkvovkuLrliqDkuIDkurrliJnpnIDlpJrmtojpmaTkuIDkuKrvvIzkvovlpoIy5Lq65bGA6ZyA5raI6ZmkN+asoe+8jDPkurrlsYDpnIDmtojpmaQ45qyh44CCYCxcbiAgICBSYW5kb21fVGFzazI6IGAtIOavj+asoeacgOWkmuWQjOaXtuWtmOWcqDHkuKrku7vliqHjgIJgLFxuICAgIFJhbmRvbV9UYXNrMzogYC0g5Lu75Yqh6aG65bqP5q+P5bGA5byA5bGA6ZqP5py655Sf5oiQ5LiA56eN77yM5L6d5qyh5Lqk5pu/6L+b6KGM44CCYCxcbiAgICBSYW5kb21fVGFzazQ6IGAtIOa2iOmZpDfkuKrmjIflrprpopzoibLlj6/ojrflvpfkuIDkuKrpmo/mnLrlvanjgIJgLFxuICAgIFJhbmRvbV9UYXNrNTogYC0g5raI6ZmkN+S4quaMh+WumuminOiJsuWPr+iOt+W+l+S4gOS4quWGsOWdl+OAgmAsXG4gICAgUmFuZG9tX1Rhc2s2OiBgLSDmtojpmaQ35Liq5oyH5a6a6aKc6Imy5Y+v6I635b6X5LiA5Liq6ZSB6ZO+44CCYCxcbiAgICBSYW5kb21fVGFzazc6IGAtIOa2iOmZpDfkuKrmjIflrprpopzoibLlj6/ojrflvpfkuIDkuKrpmo/mnLrlvaLmgIHnmoTngavnrq3pgZPlhbfjgIJgLFxuXG4gICAgLy/plIHpk77nroDku4tcbiAgICBDaGFpbnM6IGDplIHpk75gLFxuICAgIENoYWluczE6IGAxLiDmtojpmaTku7vliqHvvJrmuLjmiI/mr5TotZvlvIDlp4vlkI7vvIzmr4/kvY3njqnlrrbkvJrmlLbliLDns7vnu5/mtL7lj5HnmoTku7vliqHvvIzlrozmiJDku7vliqHkvJrlkJHlhbbku5bnjqnlrrbph4rmlL7lpqjnoo3pgZPlhbfigJTigJTplIHpk77jgIJgLFxuICAgIENoYWluczI6IGAyLiDplIHpk77nmoTlpqjnoo3mlYjmnpzvvJrpmo/mnLrplIHkvY/kuIDkuKrkvY3nva7lhoXnmoTpo5/nianjgILlj5fpmZDliLbmnJ/pl7TvvIzplIHpk77plIHkvY/nmoTkvY3nva7kuI3og73np7vliqjvvIzplIHpk77lhoXnmoTpo5/nianlj6/og73kvJrlm6DkuLrlhbbku5blnLDlnZfnmoTmtojpmaTogIzmlLnlj5jvvIzlubbpnZ7plIHkvY/lm7rlrprpo5/nianjgIJgLFxuICAgIENoYWluczM6IGAzLiDplIHpk77nmoTop6PpmaTvvJrlnKjplIHpk77pgrvov5HkvY3nva7vvIjkuIrkuIvlt6blj7Mx5qC855qE5Z2X77yJ6L+b6KGM5pmu6YCa5raI6Zmk5ZCO5oiW6ICF5Zyo6YGT5YW35pyJ5pWI5Yy65Z+f5YaF77yM5raI6ZmkMeasoeWNs+WPr+ino+mZpOOAgmAsXG5cbiAgICAvL+WGsOWdl+eugOS7i1xuICAgIEljZV9CbG9ja3M6IGDlhrDlnZdgLFxuICAgIEljZV9CbG9ja3MxOiBgMS4g5raI6Zmk5Lu75Yqh77ya5ri45oiP5q+U6LWb5byA5aeL5ZCO77yM5q+P5L2N546p5a625Lya5pS25Yiw57O757uf5rS+5Y+R55qE5Lu75Yqh77yM5a6M5oiQ5Lu75Yqh5Lya5ZCR5YW25LuW546p5a626YeK5pS+5aao56KN6YGT5YW34oCU4oCU5Yaw5Z2X44CCYCxcbiAgICBJY2VfQmxvY2tzMjogYDIuIOWGsOWdl+eahOWmqOeijeaViOaenO+8mumaj+acuuWGu+S9j+S4gOS4qumjn+eJqe+8jOWPl+WGu+acn+mXtO+8jOS9jee9ruWPr+S7peiiq+WKqOenu+WKqO+8jOeOqeWutuS4jeiDveS4u+WKqOenu+WKqOOAgmAsXG4gICAgSWNlX0Jsb2NrczM6IGAzLiDlhrDlnZfnmoTop6PpmaTvvJrlnKjlhrDlnZfpgrvov5HkvY3nva7vvIjkuIrkuIvlt6blj7Mx5qC855qE5Z2X77yJ6L+b6KGM5pmu6YCa5raI6Zmk5ZCO5oiW6ICF5Zyo6YGT5YW35pyJ5pWI5Yy65Z+f5YaFLOa2iOmZpDHmrKHljbPlj6/op6PpmaTjgIJgLFxuXG4gICAgLy/lvpfliIbnu4bliJlcbiAgICBTY29yaW5nX0RldGFpbHM6IGDlvpfliIbnu4bliJlgLFxuICAgIFNjb3JpbmdfRGV0YWlsczE6IGDmma7pgJrmtojpmaTvvJrmtojpmaTljZXlnZflvpfliIY9MjAg5YiG77ybYCxcbiAgICBTY29yaW5nX0RldGFpbHMyOiBg6L+e57ut5raI6Zmk77ya5raI6Zmk5Y2V5Z2X5b6X5YiGPei/nua2iOasoeaVsHgyMCDliIY7YCxcbiAgICBTY29yaW5nX0RldGFpbHMzOiBg54Gr566t5raI6Zmk77ya54Gr566t5r+A5rS75b6X5YiGPTMwMOWIhjvnlLHngavnrq3mtojpmaTnmoTljZXlnZflvpfliIY9MzDliIY7YCxcbiAgICBTY29yaW5nX0RldGFpbHM0OiBg54K45by55raI6Zmk77ya54K45by55r+A5rS75b6X5YiGPTUwMOWIhjvnlLHngrjlvLnmtojpmaTnmoTljZXlnZflvpfliIY9NTDliIY7YCxcbiAgICBTY29yaW5nX0RldGFpbHM1OiBg5b2p6Jm55raI6Zmk77ya5b2p6Jm55r+A5rS75b6X5YiGPTYwMOWIhjvnlLHlvanombnmtojpmaTnmoTljZXlnZflvpfliIY9NjDliIY7YCxcbiAgICBTY29yaW5nX0RldGFpbHM2OiBg54Gr566t57uE5ZCI5raI6Zmk77ya5Y+M54Gr566t5r+A5rS75b6X5YiGPTEyMDDliIY755Sx6LaF57qn54Gr566t5raI6Zmk55qE5Y2V5Z2X5b6X5YiGPTYw5YiGO2AsXG4gICAgU2NvcmluZ19EZXRhaWxzNzogYOeCuOW8ueeBq+euree7hOWQiOa2iOmZpO+8mueCuOW8ueeBq+euree7hOWQiOa/gOa0u+W+l+WIhj0xNjAw5YiG55Sx54K45by554Gr566t5raI6Zmk55qE5Y2V5Z2X5b6X5YiGPTgw5YiGO2AsXG4gICAgU2NvcmluZ19EZXRhaWxzODogYOW9qeiZueeBq+euree7hOWQiOa2iOmZpO+8muW9qeiZueeBq+euree7hOWQiOa/gOa0u+W+l+WIhj0xODAw5YiG55Sx5b2p6Jm554Gr566t5raI6Zmk55qE5Y2V5Z2X5b6X5YiGPTkw5YiGO2AsXG4gICAgU2NvcmluZ19EZXRhaWxzOTogYOeCuOW8uee7hOWQiOa2iOmZpO+8mui2hee6p+eCuOW8uea/gOa0u+W+l+WIhj0yMDAw5YiGO+eUsei2hee6p+eCuOW8uea2iOmZpOeahOWNleWdl+W+l+WIhj0xMDDliIY7YCxcbiAgICBTY29yaW5nX0RldGFpbHMxMDogYOW9qeiZueeCuOW8uea2iOmZpO+8muW9qeiZueeCuOW8uea/gOa0u+W+l+WIhj0yMjAw5YiGO+eUseW9qeiZueeCuOW8uea2iOmZpOeahOWNleWdl+W+l+WIhj0xMTDliIY7YCxcbiAgICBTY29yaW5nX0RldGFpbHMxMTogYOW9qeiZuee7hOWQiOa2iOmZpO+8mui2hee6p+W9qeiZuea/gOa0u+W+l+WIhj0yNDAw5YiGO+eUsei2hee6p+W9qeiZuea2iOmZpOeahOWNleWdl+W+l+WIhj0xMjDliIY7YCxcbn07XG5cbmNvbnN0IGNvY29zID0gY2MgYXMgYW55O1xuaWYgKCFjb2Nvcy5Kb3VfaTE4bikgY29jb3MuSm91X2kxOG4gPSB7fTtcbmNvY29zLkpvdV9pMThuLnpoX0NOID0gbGFuZ3VhZ2U7Il19