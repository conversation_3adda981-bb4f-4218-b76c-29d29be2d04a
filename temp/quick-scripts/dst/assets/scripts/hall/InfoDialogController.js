
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/InfoDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '46d58hnp4tLf7B+lcQOkRXG', 'InfoDialogController');
// scripts/hall/InfoDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
//游戏道具介绍页面
var InfoDialogController = /** @class */ (function (_super) {
    __extends(InfoDialogController, _super);
    function InfoDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.contentLay = null;
        _this.infoItem = null;
        _this.infoItem1 = null;
        _this.infoImage1 = null;
        _this.infoImage2 = null;
        _this.infoImage3 = null;
        _this.infoImage4 = null;
        _this.infoImage5 = null;
        _this.infoImage6 = null;
        _this.infoImage7 = null;
        _this.infoImage8 = null;
        _this.infoImage9 = null;
        _this.infoImage10 = null;
        _this.infoImage11 = null;
        // 规则界面相关节点
        _this.danjiScrollView = null; // 单机规则ScrollView
        _this.duorenScrollView = null; // 联机规则ScrollView
        _this.switchButton = null; // 切换按钮
        // 当前显示的规则类型：0=单机规则，1=联机规则
        _this.currentRuleType = 0;
        // 按钮的两个位置
        _this.leftPosition = cc.v2(-150, -2); // 左边位置（单机规则）
        _this.rightPosition = cc.v2(142, -2); // 右边位置（联机规则）
        _this.titleList = []; //title 的列表
        _this.tipsList = [];
        _this.generationMethodList = [];
        _this.permanentList = [];
        _this.randomList = [];
        _this.chainsList = [];
        _this.iceList = [];
        _this.scoringDetails = [];
        _this.infoImageList = [];
        _this.backCallback = null; //隐藏弹窗的回调
        return _this;
        // update (dt) {}
    }
    InfoDialogController.prototype.onLoad = function () {
        //     this.infoImageList = [
        //         this.infoImage3,
        //         this.infoImage4,
        //         this.infoImage5,
        //         this.infoImage6,
        //         this.infoImage7,
        //         this.infoImage8,
        //         this.infoImage9,
        //         this.infoImage10,
        //         this.infoImage11,
        //     ]
        //     this.titleList = [
        //         window.getLocalizedStr('Tips'),
        //         window.getLocalizedStr('Generation_Method'),
        //         window.getLocalizedStr('Permanent_Task'),
        //         window.getLocalizedStr('Random_Task'),
        //         window.getLocalizedStr('Chains'),
        //         window.getLocalizedStr('Ice_Blocks'),
        //         window.getLocalizedStr('Scoring_Details'),
        //     ]//title 的列表
        //     this. tipsList = [
        //         window.getLocalizedStr('Tips1'),
        //         window.getLocalizedStr('Tips2'),
        //         window.getLocalizedStr('Tips3'),
        //         window.getLocalizedStr('Tips4'),
        //         window.getLocalizedStr('Tips5'),
        //     ]
        //     this.generationMethodList = [
        //         window.getLocalizedStr('Generation_Method1'),
        //         window.getLocalizedStr('Generation_Method2'),
        //         window.getLocalizedStr('Generation_Method3'),
        //         window.getLocalizedStr('Generation_Method4'),
        //         window.getLocalizedStr('Generation_Method5'),
        //         window.getLocalizedStr('Generation_Method6'),
        //         window.getLocalizedStr('Generation_Method7'),
        //         window.getLocalizedStr('Generation_Method8'),
        //         window.getLocalizedStr('Generation_Method9'),
        //     ]
        //     this.permanentList = [
        //         window.getLocalizedStr('Permanent_Task1'),
        //     ]
        //     this.randomList = [
        //         window.getLocalizedStr('Random_Task1'),
        //         window.getLocalizedStr('Random_Task2'),
        //         window.getLocalizedStr('Random_Task3'),
        //         window.getLocalizedStr('Random_Task4'),
        //         window.getLocalizedStr('Random_Task5'),
        //         window.getLocalizedStr('Random_Task6'),
        //         window.getLocalizedStr('Random_Task7'),
        //     ]
        //     this.chainsList = [
        //         window.getLocalizedStr('Chains1'),
        //         window.getLocalizedStr('Chains2'),
        //         window.getLocalizedStr('Chains3'),
        //     ]
        //     this.iceList = [
        //         window.getLocalizedStr('Ice_Blocks1'),
        //         window.getLocalizedStr('Ice_Blocks2'),
        //         window.getLocalizedStr('Ice_Blocks3'),
        //     ]
        //     this.scoringDetails = [
        //         window.getLocalizedStr('Scoring_Details1'),
        //         window.getLocalizedStr('Scoring_Details2'),
        //         window.getLocalizedStr('Scoring_Details3'),
        //         window.getLocalizedStr('Scoring_Details4'),
        //         window.getLocalizedStr('Scoring_Details5'),
        //         window.getLocalizedStr('Scoring_Details6'),
        //         window.getLocalizedStr('Scoring_Details7'),
        //         window.getLocalizedStr('Scoring_Details8'),
        //         window.getLocalizedStr('Scoring_Details9'),
        //         window.getLocalizedStr('Scoring_Details10'),
        //         window.getLocalizedStr('Scoring_Details11'),
        //     ]
        // 初始化规则界面
        this.initRuleInterface();
    };
    InfoDialogController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        //     this.contentLay.removeAllChildren()
        //     this.getTitleNode(this.titleList[0])
        //     this.tipsList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[1])
        //     this.generationMethodList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         let infoImg = cc.instantiate(this.infoImageList[index])
        //         infoItemOneController.setimgNode(infoImg)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[2])
        //     this.permanentList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     let infoImg1 = cc.instantiate(this.infoImage1)
        //     this.contentLay.addChild(infoImg1)
        //     this.getTitleNode(this.titleList[3])
        //     this.randomList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     let infoImg2 = cc.instantiate(this.infoImage2)
        //     this.contentLay.addChild(infoImg2)
        //     this.getTitleNode(this.titleList[4])
        //     this.chainsList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[5])
        //     this.iceList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[6])
        //     this.scoringDetails.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        // }
        // getTitleNode(title: string) {
        //     let infoItem = cc.instantiate(this.infoItem);//初始化一个预制体
        //     let infoItemController = infoItem.getComponent(InfoItemController)
        //     infoItemController.setContent(title)
        //     this.contentLay.addChild(infoItem)
        // }
    };
    InfoDialogController.prototype.show = function (backCallback) {
        this.backCallback = backCallback;
        this.node.active = true;
        this.boardBg.scale = 0;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    InfoDialogController.prototype.hide = function () {
        var _this = this;
        if (this.backCallback) {
            this.backCallback();
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    /**
     * 初始化规则界面
     */
    InfoDialogController.prototype.initRuleInterface = function () {
        // 设置按钮点击事件
        if (this.switchButton) {
            this.switchButton.on('click', this.onSwitchButtonClick, this);
        }
        // 初始化显示状态 - 默认显示单机规则（按钮在左边）
        this.switchToRuleType(0);
    };
    /**
     * 切换按钮点击事件
     */
    InfoDialogController.prototype.onSwitchButtonClick = function () {
        cc.log("点击切换按钮");
        // 切换到另一种规则类型
        var newRuleType = this.currentRuleType === 0 ? 1 : 0;
        this.switchToRuleType(newRuleType);
    };
    /**
     * 切换规则类型
     * @param ruleType 0=单机规则，1=联机规则
     */
    InfoDialogController.prototype.switchToRuleType = function (ruleType) {
        if (this.currentRuleType === ruleType) {
            return; // 已经是当前类型，不需要切换
        }
        cc.log("\u5207\u6362\u89C4\u5219\u7C7B\u578B: " + this.currentRuleType + " -> " + ruleType);
        this.currentRuleType = ruleType;
        // 执行滑动动画切换
        this.animateRuleSwitch(ruleType);
    };
    /**
     * 执行规则切换的滑动动画
     * @param targetRuleType 目标规则类型
     */
    InfoDialogController.prototype.animateRuleSwitch = function (targetRuleType) {
        var _this = this;
        var animationDuration = 0.3; // 动画持续时间
        if (targetRuleType === 0) {
            // 切换到单机规则
            cc.log("显示单机规则，隐藏联机规则");
            // 显示单机规则ScrollView
            if (this.danjiScrollView) {
                this.danjiScrollView.active = true;
                // 从右边滑入
                this.danjiScrollView.x = 300;
                cc.tween(this.danjiScrollView)
                    .to(animationDuration, { x: 0 }, { easing: 'quartOut' })
                    .start();
            }
            // 隐藏联机规则ScrollView
            if (this.duorenScrollView) {
                // 向左滑出
                cc.tween(this.duorenScrollView)
                    .to(animationDuration, { x: -300 }, { easing: 'quartOut' })
                    .call(function () {
                    _this.duorenScrollView.active = false;
                    _this.duorenScrollView.x = 0; // 重置位置
                })
                    .start();
            }
        }
        else {
            // 切换到联机规则
            cc.log("显示联机规则，隐藏单机规则");
            // 显示联机规则ScrollView
            if (this.duorenScrollView) {
                this.duorenScrollView.active = true;
                // 从右边滑入
                this.duorenScrollView.x = 300;
                cc.tween(this.duorenScrollView)
                    .to(animationDuration, { x: 0 }, { easing: 'quartOut' })
                    .start();
            }
            // 隐藏单机规则ScrollView
            if (this.danjiScrollView) {
                // 向左滑出
                cc.tween(this.danjiScrollView)
                    .to(animationDuration, { x: -300 }, { easing: 'quartOut' })
                    .call(function () {
                    _this.danjiScrollView.active = false;
                    _this.danjiScrollView.x = 0; // 重置位置
                })
                    .start();
            }
        }
    };
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoItem", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoItem1", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage1", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage2", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage3", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage4", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage5", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage6", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage7", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage8", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage9", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage10", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage11", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "danjiScrollView", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "duorenScrollView", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "switchButton", void 0);
    InfoDialogController = __decorate([
        ccclass
    ], InfoDialogController);
    return InfoDialogController;
}(cc.Component));
exports.default = InfoDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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