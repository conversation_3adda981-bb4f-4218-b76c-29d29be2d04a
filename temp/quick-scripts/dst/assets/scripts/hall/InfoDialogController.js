
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/InfoDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '46d58hnp4tLf7B+lcQOkRXG', 'InfoDialogController');
// scripts/hall/InfoDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
//游戏道具介绍页面
var InfoDialogController = /** @class */ (function (_super) {
    __extends(InfoDialogController, _super);
    function InfoDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.contentLay = null;
        _this.infoItem = null;
        _this.infoItem1 = null;
        _this.infoImage1 = null;
        _this.infoImage2 = null;
        _this.infoImage3 = null;
        _this.infoImage4 = null;
        _this.infoImage5 = null;
        _this.infoImage6 = null;
        _this.infoImage7 = null;
        _this.infoImage8 = null;
        _this.infoImage9 = null;
        _this.infoImage10 = null;
        _this.infoImage11 = null;
        // 规则界面相关节点
        _this.danjiScrollView = null; // 单机规则ScrollView
        _this.duorenScrollView = null; // 联机规则ScrollView
        _this.switchButton = null; // 移动的切换按钮
        _this.leftTabButton = null; // rank_tab的左边按钮
        _this.rightTabButton = null; // rank_tab的右边按钮
        // 当前显示的规则类型：0=单机规则，1=联机规则
        _this.currentRuleType = 0;
        // 按钮的两个位置
        _this.leftPosition = cc.v2(-150, -2); // 左边位置（单机规则）
        _this.rightPosition = cc.v2(142, -2); // 右边位置（联机规则）
        _this.titleList = []; //title 的列表
        _this.tipsList = [];
        _this.generationMethodList = [];
        _this.permanentList = [];
        _this.randomList = [];
        _this.chainsList = [];
        _this.iceList = [];
        _this.scoringDetails = [];
        _this.infoImageList = [];
        _this.backCallback = null; //隐藏弹窗的回调
        return _this;
        // update (dt) {}
    }
    InfoDialogController.prototype.onLoad = function () {
        //     this.infoImageList = [
        //         this.infoImage3,
        //         this.infoImage4,
        //         this.infoImage5,
        //         this.infoImage6,
        //         this.infoImage7,
        //         this.infoImage8,
        //         this.infoImage9,
        //         this.infoImage10,
        //         this.infoImage11,
        //     ]
        //     this.titleList = [
        //         window.getLocalizedStr('Tips'),
        //         window.getLocalizedStr('Generation_Method'),
        //         window.getLocalizedStr('Permanent_Task'),
        //         window.getLocalizedStr('Random_Task'),
        //         window.getLocalizedStr('Chains'),
        //         window.getLocalizedStr('Ice_Blocks'),
        //         window.getLocalizedStr('Scoring_Details'),
        //     ]//title 的列表
        //     this. tipsList = [
        //         window.getLocalizedStr('Tips1'),
        //         window.getLocalizedStr('Tips2'),
        //         window.getLocalizedStr('Tips3'),
        //         window.getLocalizedStr('Tips4'),
        //         window.getLocalizedStr('Tips5'),
        //     ]
        //     this.generationMethodList = [
        //         window.getLocalizedStr('Generation_Method1'),
        //         window.getLocalizedStr('Generation_Method2'),
        //         window.getLocalizedStr('Generation_Method3'),
        //         window.getLocalizedStr('Generation_Method4'),
        //         window.getLocalizedStr('Generation_Method5'),
        //         window.getLocalizedStr('Generation_Method6'),
        //         window.getLocalizedStr('Generation_Method7'),
        //         window.getLocalizedStr('Generation_Method8'),
        //         window.getLocalizedStr('Generation_Method9'),
        //     ]
        //     this.permanentList = [
        //         window.getLocalizedStr('Permanent_Task1'),
        //     ]
        //     this.randomList = [
        //         window.getLocalizedStr('Random_Task1'),
        //         window.getLocalizedStr('Random_Task2'),
        //         window.getLocalizedStr('Random_Task3'),
        //         window.getLocalizedStr('Random_Task4'),
        //         window.getLocalizedStr('Random_Task5'),
        //         window.getLocalizedStr('Random_Task6'),
        //         window.getLocalizedStr('Random_Task7'),
        //     ]
        //     this.chainsList = [
        //         window.getLocalizedStr('Chains1'),
        //         window.getLocalizedStr('Chains2'),
        //         window.getLocalizedStr('Chains3'),
        //     ]
        //     this.iceList = [
        //         window.getLocalizedStr('Ice_Blocks1'),
        //         window.getLocalizedStr('Ice_Blocks2'),
        //         window.getLocalizedStr('Ice_Blocks3'),
        //     ]
        //     this.scoringDetails = [
        //         window.getLocalizedStr('Scoring_Details1'),
        //         window.getLocalizedStr('Scoring_Details2'),
        //         window.getLocalizedStr('Scoring_Details3'),
        //         window.getLocalizedStr('Scoring_Details4'),
        //         window.getLocalizedStr('Scoring_Details5'),
        //         window.getLocalizedStr('Scoring_Details6'),
        //         window.getLocalizedStr('Scoring_Details7'),
        //         window.getLocalizedStr('Scoring_Details8'),
        //         window.getLocalizedStr('Scoring_Details9'),
        //         window.getLocalizedStr('Scoring_Details10'),
        //         window.getLocalizedStr('Scoring_Details11'),
        //     ]
        // 初始化规则界面
        this.initRuleInterface();
    };
    InfoDialogController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        //     this.contentLay.removeAllChildren()
        //     this.getTitleNode(this.titleList[0])
        //     this.tipsList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[1])
        //     this.generationMethodList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         let infoImg = cc.instantiate(this.infoImageList[index])
        //         infoItemOneController.setimgNode(infoImg)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[2])
        //     this.permanentList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     let infoImg1 = cc.instantiate(this.infoImage1)
        //     this.contentLay.addChild(infoImg1)
        //     this.getTitleNode(this.titleList[3])
        //     this.randomList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     let infoImg2 = cc.instantiate(this.infoImage2)
        //     this.contentLay.addChild(infoImg2)
        //     this.getTitleNode(this.titleList[4])
        //     this.chainsList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[5])
        //     this.iceList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[6])
        //     this.scoringDetails.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        // }
        // getTitleNode(title: string) {
        //     let infoItem = cc.instantiate(this.infoItem);//初始化一个预制体
        //     let infoItemController = infoItem.getComponent(InfoItemController)
        //     infoItemController.setContent(title)
        //     this.contentLay.addChild(infoItem)
        // }
    };
    InfoDialogController.prototype.show = function (backCallback) {
        this.backCallback = backCallback;
        this.node.active = true;
        this.boardBg.scale = 0;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    InfoDialogController.prototype.hide = function () {
        var _this = this;
        if (this.backCallback) {
            this.backCallback();
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    /**
     * 初始化规则界面
     */
    InfoDialogController.prototype.initRuleInterface = function () {
        // 设置左边tab按钮点击事件
        if (this.leftTabButton) {
            this.leftTabButton.on('click', this.onLeftTabButtonClick, this);
        }
        // 设置右边tab按钮点击事件
        if (this.rightTabButton) {
            this.rightTabButton.on('click', this.onRightTabButtonClick, this);
        }
        // 初始化显示状态 - 默认显示单机规则（切换按钮在左边）
        this.switchToRuleType(0);
    };
    /**
     * 左边tab按钮点击事件（单机规则）
     */
    InfoDialogController.prototype.onLeftTabButtonClick = function () {
        cc.log("点击左边tab按钮 - 切换到单机规则");
        this.switchToRuleType(0);
    };
    /**
     * 右边tab按钮点击事件（联机规则）
     */
    InfoDialogController.prototype.onRightTabButtonClick = function () {
        cc.log("点击右边tab按钮 - 切换到联机规则");
        this.switchToRuleType(1);
    };
    /**
     * 切换规则类型
     * @param ruleType 0=单机规则，1=联机规则
     */
    InfoDialogController.prototype.switchToRuleType = function (ruleType) {
        if (this.currentRuleType === ruleType) {
            return; // 已经是当前类型，不需要切换
        }
        cc.log("\u5207\u6362\u89C4\u5219\u7C7B\u578B: " + this.currentRuleType + " -> " + ruleType);
        this.currentRuleType = ruleType;
        // 执行滑动动画切换
        this.animateRuleSwitch(ruleType);
    };
    /**
     * 执行规则切换的滑动动画
     * @param targetRuleType 目标规则类型
     */
    InfoDialogController.prototype.animateRuleSwitch = function (targetRuleType) {
        var _this = this;
        var animationDuration = 0.3; // 动画持续时间
        // 移动按钮到对应位置
        if (this.switchButton) {
            var targetPosition = targetRuleType === 0 ? this.leftPosition : this.rightPosition;
            cc.log("\u6309\u94AE\u79FB\u52A8\u5230\u4F4D\u7F6E: (" + targetPosition.x + ", " + targetPosition.y + ")");
            cc.tween(this.switchButton)
                .to(animationDuration, {
                x: targetPosition.x,
                y: targetPosition.y
            }, { easing: 'quartOut' })
                .start();
        }
        if (targetRuleType === 0) {
            // 切换到单机规则
            cc.log("显示单机规则，隐藏联机规则");
            // 显示单机规则ScrollView
            if (this.danjiScrollView) {
                this.danjiScrollView.active = true;
                // 从右边滑入
                this.danjiScrollView.x = 300;
                cc.tween(this.danjiScrollView)
                    .to(animationDuration, { x: 0 }, { easing: 'quartOut' })
                    .start();
            }
            // 隐藏联机规则ScrollView
            if (this.duorenScrollView) {
                // 向左滑出
                cc.tween(this.duorenScrollView)
                    .to(animationDuration, { x: -300 }, { easing: 'quartOut' })
                    .call(function () {
                    _this.duorenScrollView.active = false;
                    _this.duorenScrollView.x = 0; // 重置位置
                })
                    .start();
            }
        }
        else {
            // 切换到联机规则
            cc.log("显示联机规则，隐藏单机规则");
            // 显示联机规则ScrollView
            if (this.duorenScrollView) {
                this.duorenScrollView.active = true;
                // 从右边滑入
                this.duorenScrollView.x = 300;
                cc.tween(this.duorenScrollView)
                    .to(animationDuration, { x: 0 }, { easing: 'quartOut' })
                    .start();
            }
            // 隐藏单机规则ScrollView
            if (this.danjiScrollView) {
                // 向左滑出
                cc.tween(this.danjiScrollView)
                    .to(animationDuration, { x: -300 }, { easing: 'quartOut' })
                    .call(function () {
                    _this.danjiScrollView.active = false;
                    _this.danjiScrollView.x = 0; // 重置位置
                })
                    .start();
            }
        }
    };
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoItem", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoItem1", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage1", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage2", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage3", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage4", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage5", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage6", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage7", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage8", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage9", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage10", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage11", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "danjiScrollView", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "duorenScrollView", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "switchButton", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "leftTabButton", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "rightTabButton", void 0);
    InfoDialogController = __decorate([
        ccclass
    ], InfoDialogController);
    return InfoDialogController;
}(cc.Component));
exports.default = InfoDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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