
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/InfoDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '46d58hnp4tLf7B+lcQOkRXG', 'InfoDialogController');
// scripts/hall/InfoDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
//游戏道具介绍页面
var InfoDialogController = /** @class */ (function (_super) {
    __extends(InfoDialogController, _super);
    function InfoDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.contentLay = null;
        _this.infoItem = null;
        _this.infoItem1 = null;
        _this.infoImage1 = null;
        _this.infoImage2 = null;
        _this.infoImage3 = null;
        _this.infoImage4 = null;
        _this.infoImage5 = null;
        _this.infoImage6 = null;
        _this.infoImage7 = null;
        _this.infoImage8 = null;
        _this.infoImage9 = null;
        _this.infoImage10 = null;
        _this.infoImage11 = null;
        _this.titleList = []; //title 的列表
        _this.tipsList = [];
        _this.generationMethodList = [];
        _this.permanentList = [];
        _this.randomList = [];
        _this.chainsList = [];
        _this.iceList = [];
        _this.scoringDetails = [];
        _this.infoImageList = [];
        _this.backCallback = null; //隐藏弹窗的回调
        return _this;
        // update (dt) {}
    }
    InfoDialogController.prototype.onLoad = function () {
        //     this.infoImageList = [
        //         this.infoImage3,
        //         this.infoImage4,
        //         this.infoImage5,
        //         this.infoImage6,
        //         this.infoImage7,
        //         this.infoImage8,
        //         this.infoImage9,
        //         this.infoImage10,
        //         this.infoImage11,
        //     ]
        //     this.titleList = [
        //         window.getLocalizedStr('Tips'),
        //         window.getLocalizedStr('Generation_Method'),
        //         window.getLocalizedStr('Permanent_Task'),
        //         window.getLocalizedStr('Random_Task'),
        //         window.getLocalizedStr('Chains'),
        //         window.getLocalizedStr('Ice_Blocks'),
        //         window.getLocalizedStr('Scoring_Details'),
        //     ]//title 的列表
        //     this. tipsList = [
        //         window.getLocalizedStr('Tips1'),
        //         window.getLocalizedStr('Tips2'),
        //         window.getLocalizedStr('Tips3'),
        //         window.getLocalizedStr('Tips4'),
        //         window.getLocalizedStr('Tips5'),
        //     ]
        //     this.generationMethodList = [
        //         window.getLocalizedStr('Generation_Method1'),
        //         window.getLocalizedStr('Generation_Method2'),
        //         window.getLocalizedStr('Generation_Method3'),
        //         window.getLocalizedStr('Generation_Method4'),
        //         window.getLocalizedStr('Generation_Method5'),
        //         window.getLocalizedStr('Generation_Method6'),
        //         window.getLocalizedStr('Generation_Method7'),
        //         window.getLocalizedStr('Generation_Method8'),
        //         window.getLocalizedStr('Generation_Method9'),
        //     ]
        //     this.permanentList = [
        //         window.getLocalizedStr('Permanent_Task1'),
        //     ]
        //     this.randomList = [
        //         window.getLocalizedStr('Random_Task1'),
        //         window.getLocalizedStr('Random_Task2'),
        //         window.getLocalizedStr('Random_Task3'),
        //         window.getLocalizedStr('Random_Task4'),
        //         window.getLocalizedStr('Random_Task5'),
        //         window.getLocalizedStr('Random_Task6'),
        //         window.getLocalizedStr('Random_Task7'),
        //     ]
        //     this.chainsList = [
        //         window.getLocalizedStr('Chains1'),
        //         window.getLocalizedStr('Chains2'),
        //         window.getLocalizedStr('Chains3'),
        //     ]
        //     this.iceList = [
        //         window.getLocalizedStr('Ice_Blocks1'),
        //         window.getLocalizedStr('Ice_Blocks2'),
        //         window.getLocalizedStr('Ice_Blocks3'),
        //     ]
        //     this.scoringDetails = [
        //         window.getLocalizedStr('Scoring_Details1'),
        //         window.getLocalizedStr('Scoring_Details2'),
        //         window.getLocalizedStr('Scoring_Details3'),
        //         window.getLocalizedStr('Scoring_Details4'),
        //         window.getLocalizedStr('Scoring_Details5'),
        //         window.getLocalizedStr('Scoring_Details6'),
        //         window.getLocalizedStr('Scoring_Details7'),
        //         window.getLocalizedStr('Scoring_Details8'),
        //         window.getLocalizedStr('Scoring_Details9'),
        //         window.getLocalizedStr('Scoring_Details10'),
        //         window.getLocalizedStr('Scoring_Details11'),
        //     ]
    };
    InfoDialogController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        //     this.contentLay.removeAllChildren()
        //     this.getTitleNode(this.titleList[0])
        //     this.tipsList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[1])
        //     this.generationMethodList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         let infoImg = cc.instantiate(this.infoImageList[index])
        //         infoItemOneController.setimgNode(infoImg)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[2])
        //     this.permanentList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     let infoImg1 = cc.instantiate(this.infoImage1)
        //     this.contentLay.addChild(infoImg1)
        //     this.getTitleNode(this.titleList[3])
        //     this.randomList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     let infoImg2 = cc.instantiate(this.infoImage2)
        //     this.contentLay.addChild(infoImg2)
        //     this.getTitleNode(this.titleList[4])
        //     this.chainsList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[5])
        //     this.iceList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[6])
        //     this.scoringDetails.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        // }
        // getTitleNode(title: string) {
        //     let infoItem = cc.instantiate(this.infoItem);//初始化一个预制体
        //     let infoItemController = infoItem.getComponent(InfoItemController)
        //     infoItemController.setContent(title)
        //     this.contentLay.addChild(infoItem)
        // }
    };
    InfoDialogController.prototype.show = function (backCallback) {
        this.backCallback = backCallback;
        this.node.active = true;
        this.boardBg.scale = 0;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    InfoDialogController.prototype.hide = function () {
        var _this = this;
        if (this.backCallback) {
            this.backCallback();
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoItem", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoItem1", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage1", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage2", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage3", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage4", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage5", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage6", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage7", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage8", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage9", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage10", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage11", void 0);
    InfoDialogController = __decorate([
        ccclass
    ], InfoDialogController);
    return InfoDialogController;
}(cc.Component));
exports.default = InfoDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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