
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/LevelSelectController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '40c9e3ykUFClLHG7s1cQEKm', 'LevelSelectController');
// scripts/hall/Level/LevelSelectController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LevelStatus = void 0;
var ScrollViewHelper_1 = require("./ScrollViewHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 关卡状态枚举
var LevelStatus;
(function (LevelStatus) {
    LevelStatus[LevelStatus["LOCKED"] = 0] = "LOCKED";
    LevelStatus[LevelStatus["CURRENT"] = 1] = "CURRENT";
    LevelStatus[LevelStatus["COMPLETED"] = 2] = "COMPLETED"; // 已通关（绿色）
})(LevelStatus = exports.LevelStatus || (exports.LevelStatus = {}));
var LevelSelectController = /** @class */ (function (_super) {
    __extends(LevelSelectController, _super);
    function LevelSelectController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.scrollView = null;
        _this.content = null;
        // 关卡数据
        _this.levelDataList = [];
        _this.currentSelectedLevel = 1;
        _this.totalLevels = 30;
        _this.screenWidth = 650;
        _this.levelItemWidth = 150; // 关卡项的宽度（包括间距）
        _this.visibleLevels = 3; // 可见关卡数量
        // 连接线配置（公开，方便调试）
        _this.lineCount = 9; // 每两个关卡之间的连接线数量
        _this.lineSpacing = 16; // 连接线之间的间距
        _this.levelToLineDistance = 8; // 关卡到连接线的距离
        // 关卡节点列表
        _this.levelNodes = [];
        // 滑动状态标志
        _this.isAutoScrolling = false;
        // 关卡选择变化回调
        _this.onLevelSelectionChanged = null;
        return _this;
    }
    LevelSelectController.prototype.onLoad = function () {
        // 修复ScrollView的Scrollbar问题
        this.fixScrollViewScrollbar();
        this.initLevelData();
        this.createLevelItems();
        this.updateLevelDisplay();
        this.setupScrollEvents();
        // 设置初始滚动位置为第1关（最左边）
        if (this.scrollView) {
            this.scrollView.scrollToPercentHorizontal(0, 0);
        }
    };
    LevelSelectController.prototype.start = function () {
        var _this = this;
        // 延迟滚动到第一关，确保界面完全初始化
        this.scheduleOnce(function () {
            // 确保滚动到第1关
            _this.scrollToLevel(1);
        }, 0.1);
    };
    LevelSelectController.prototype.onDestroy = function () {
        // 移除事件监听器
        if (this.scrollView) {
            this.scrollView.node.off('scrolling', this.onScrolling, this);
            this.scrollView.node.off('scroll-ended', this.onScrollEnded, this);
        }
    };
    /**
     * 初始化关卡数据
     */
    LevelSelectController.prototype.initLevelData = function () {
        this.levelDataList = [];
        // 初始化关卡状态：第1关为当前可玩关卡，其他为锁定状态
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i === 1) {
                status = LevelStatus.CURRENT; // 第1关默认为当前可玩关卡（显示开始游戏按钮）
            }
            else {
                status = LevelStatus.LOCKED; // 其他关卡锁定
            }
            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }
        // 默认选中第一关
        this.currentSelectedLevel = 1;
    };
    /**
     * 创建关卡项目
     */
    LevelSelectController.prototype.createLevelItems = function () {
        if (!this.content) {
            cc.error("Content node is not assigned!");
            return;
        }
        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];
        // 计算总宽度
        var totalWidth = (this.totalLevels - 1) * this.levelItemWidth + this.screenWidth;
        this.content.width = totalWidth;
        for (var i = 0; i < this.totalLevels; i++) {
            var levelData = this.levelDataList[i];
            // 创建关卡节点
            var levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);
            // 设置位置
            var posX = i * this.levelItemWidth - totalWidth / 2 + this.screenWidth / 2;
            levelNode.setPosition(posX, 0);
            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                this.createConnectionLines(i, posX);
            }
        }
    };
    /**
     * 创建关卡节点
     */
    LevelSelectController.prototype.createLevelNode = function (levelData) {
        var node = new cc.Node("Level_" + levelData.levelNumber);
        // 添加Sprite组件
        var sprite = node.addComponent(cc.Sprite);
        // 添加Label组件显示关卡数字
        var labelNode = new cc.Node("LevelLabel");
        var label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();
        // 设置字体样式
        label.fontSize = 30;
        label.node.color = cc.color(255, 255, 255); // #FFFFFF
        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        label.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 添加外边框
        var outline = label.addComponent(cc.LabelOutline);
        this.updateLabelOutline(outline, levelData.status);
        labelNode.parent = node;
        labelNode.setPosition(0, 0); // 居中对齐
        // 添加Button组件
        var button = node.addComponent(cc.Button);
        button.target = node;
        // 设置点击事件
        var eventHandler = new cc.Component.EventHandler();
        eventHandler.target = this.node;
        eventHandler.component = "LevelSelectController";
        eventHandler.handler = "onLevelClicked";
        eventHandler.customEventData = levelData.levelNumber.toString();
        button.clickEvents.push(eventHandler);
        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);
        return node;
    };
    /**
     * 创建连接线组（9个连接线）
     */
    LevelSelectController.prototype.createConnectionLines = function (levelIndex, levelPosX) {
        var startX = levelPosX + 46 / 2 + this.levelToLineDistance; // 关卡右边缘 + 距离
        var totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        var endX = levelPosX + this.levelItemWidth - 46 / 2 - this.levelToLineDistance; // 下一个关卡左边缘 - 距离
        var availableWidth = endX - startX;
        // 如果可用宽度小于需要的宽度，调整间距
        var actualSpacing = this.lineSpacing;
        if (totalLineWidth > availableWidth) {
            actualSpacing = availableWidth / (this.lineCount - 1);
        }
        for (var i = 0; i < this.lineCount; i++) {
            var lineNode = this.createSingleLineNode();
            this.content.addChild(lineNode);
            var lineX = startX + i * actualSpacing;
            lineNode.setPosition(lineX, 0);
        }
    };
    /**
     * 创建单个连接线节点
     */
    LevelSelectController.prototype.createSingleLineNode = function () {
        var node = new cc.Node("Line");
        var sprite = node.addComponent(cc.Sprite);
        // 设置连接线大小为6*6
        node.setContentSize(6, 6);
        // 加载连接线图片
        cc.resources.load("hall_page_res/Level_Btn/pop_line", cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 图片加载后重新设置大小为6x6，确保不被图片原始大小覆盖
                node.setContentSize(6, 6);
            }
        });
        return node;
    };
    /**
     * 检查是否为特殊关卡（第5、10、15、20、25关）
     */
    LevelSelectController.prototype.isSpecialLevel = function (levelNumber) {
        return levelNumber % 5 === 0;
    };
    /**
     * 更新关卡节点外观
     */
    LevelSelectController.prototype.updateLevelNodeAppearance = function (node, levelData, isSelected) {
        var sprite = node.getComponent(cc.Sprite);
        if (!sprite)
            return;
        var imagePath = "";
        var size = cc.size(46, 46);
        // 检查是否为特殊关卡（第5、10、15、20、25关）
        var isSpecialLevel = this.isSpecialLevel(levelData.levelNumber);
        var uiSuffix = isSpecialLevel ? "01" : "";
        // 根据状态和是否选中确定图片路径
        if (isSelected) {
            size = cc.size(86, 86);
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix + "_choose";
                    break;
                case LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix + "_choose";
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix + "_choose";
                    break;
            }
        }
        else {
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix;
                    break;
                case LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix;
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix;
                    break;
            }
        }
        // 设置节点大小
        node.setContentSize(size);
        // 加载并设置图片
        cc.resources.load(imagePath, cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 图片加载后强制设置正确的大小，覆盖图片原始大小
                node.setContentSize(size);
            }
        });
        // 更新标签外边框
        var labelNode = node.getChildByName("LevelLabel");
        if (labelNode) {
            var label = labelNode.getComponent(cc.Label);
            if (label) {
                var outline = label.getComponent(cc.LabelOutline);
                if (outline) {
                    this.updateLabelOutline(outline, levelData.status);
                }
            }
        }
    };
    /**
     * 更新关卡显示
     */
    LevelSelectController.prototype.updateLevelDisplay = function () {
        for (var i = 0; i < this.levelNodes.length; i++) {
            var node = this.levelNodes[i];
            var levelData = this.levelDataList[i];
            var isSelected = (levelData.levelNumber === this.currentSelectedLevel);
            this.updateLevelNodeAppearance(node, levelData, isSelected);
        }
    };
    /**
     * 滚动到指定关卡
     */
    LevelSelectController.prototype.scrollToLevel = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        var targetIndex = levelNumber - 1;
        var contentWidth = this.content.width;
        var scrollViewWidth = this.scrollView.node.width;
        // 计算目标位置的偏移量
        var targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);
        // 限制偏移量在有效范围内
        var clampedOffset = cc.misc.clampf(targetOffset, 0, 1);
        // 设置滚动位置
        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);
    };
    /**
     * 关卡点击事件处理
     */
    LevelSelectController.prototype.onLevelClicked = function (event, customEventData) {
        var levelNumber = parseInt(customEventData);
        // 允许选择任何关卡（包括未解锁的）
        // 更新当前选中关卡
        this.currentSelectedLevel = levelNumber;
        this.updateLevelDisplay();
        // 滚动到选中关卡
        this.isAutoScrolling = true;
        this.scrollToLevel(levelNumber);
        // 通知关卡选择变化
        if (this.onLevelSelectionChanged) {
            this.onLevelSelectionChanged(levelNumber);
        }
        // 这里可以添加进入关卡的逻辑
        // this.enterLevel(levelNumber);
    };
    /**
     * 设置关卡状态
     */
    LevelSelectController.prototype.setLevelStatus = function (levelNumber, status) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        this.levelDataList[levelNumber - 1].status = status;
        this.updateLevelDisplay();
    };
    /**
     * 获取当前选中的关卡
     */
    LevelSelectController.prototype.getCurrentSelectedLevel = function () {
        return this.currentSelectedLevel;
    };
    /**
     * 获取指定关卡的数据
     */
    LevelSelectController.prototype.getLevelData = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return null;
        return this.levelDataList[levelNumber - 1];
    };
    /**
     * 设置关卡进度（从外部调用，比如从后端获取数据后）
     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels
     */
    LevelSelectController.prototype.setLevelProgress = function (levelProgressData) {
        if (!levelProgressData)
            return;
        var clearedLevels = levelProgressData.clearedLevels, currentLevel = levelProgressData.currentLevel, totalLevels = levelProgressData.totalLevels;
        // 验证数据有效性
        if (clearedLevels < 0 || currentLevel < 1 || currentLevel > totalLevels)
            return;
        // 更新总关卡数（如果后端传了的话）
        if (totalLevels && totalLevels > 0) {
            this.totalLevels = totalLevels;
        }
        // 重新设置所有关卡状态
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i < currentLevel) {
                status = LevelStatus.COMPLETED; // 当前关卡之前的都是已完成（绿色）
            }
            else if (i === currentLevel) {
                status = LevelStatus.CURRENT; // 当前关卡（黄色选择UI）
            }
            else {
                status = LevelStatus.LOCKED; // 当前关卡之后的都是未解锁（灰色）
            }
            this.levelDataList[i - 1].status = status;
        }
        // 更新当前选中关卡为后端指定的currentLevel
        this.currentSelectedLevel = currentLevel;
        this.updateLevelDisplay();
        this.scrollToLevel(currentLevel);
        // 通知关卡选择变化
        if (this.onLevelSelectionChanged) {
            this.onLevelSelectionChanged(currentLevel);
        }
    };
    /**
     * 设置关卡进度（兼容旧接口）
     * @param completedLevels 已完成的关卡数
     */
    LevelSelectController.prototype.setLevelProgressLegacy = function (completedLevels) {
        // 转换为新的数据格式
        var levelProgressData = {
            clearedLevels: completedLevels,
            currentLevel: Math.min(completedLevels + 1, this.totalLevels),
            totalLevels: this.totalLevels
        };
        this.setLevelProgress(levelProgressData);
    };
    /**
     * 解锁下一关
     */
    LevelSelectController.prototype.unlockNextLevel = function () {
        for (var i = 0; i < this.levelDataList.length; i++) {
            if (this.levelDataList[i].status === LevelStatus.LOCKED) {
                this.levelDataList[i].status = LevelStatus.CURRENT;
                this.updateLevelDisplay();
                break;
            }
        }
    };
    /**
     * 完成当前关卡
     */
    LevelSelectController.prototype.completeCurrentLevel = function () {
        var currentLevelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (currentLevelData.status === LevelStatus.CURRENT) {
            currentLevelData.status = LevelStatus.COMPLETED;
            this.unlockNextLevel();
            this.updateLevelDisplay();
        }
    };
    /**
     * 设置滑动事件监听
     */
    LevelSelectController.prototype.setupScrollEvents = function () {
        if (!this.scrollView)
            return;
        // 监听滑动中事件
        this.scrollView.node.on('scrolling', this.onScrolling, this);
        // 监听滑动结束事件
        this.scrollView.node.on('scroll-ended', this.onScrollEnded, this);
    };
    /**
     * 滑动中事件处理
     */
    LevelSelectController.prototype.onScrolling = function () {
        this.updateSelectedLevelByPosition();
    };
    /**
     * 滑动结束事件处理
     */
    LevelSelectController.prototype.onScrollEnded = function () {
        // 如果是自动滚动触发的事件，忽略
        if (this.isAutoScrolling) {
            this.isAutoScrolling = false;
            return;
        }
        this.updateSelectedLevelByPosition();
        // 滑动结束后，将选中的关卡固定居中
        this.isAutoScrolling = true;
        this.scrollToLevel(this.currentSelectedLevel);
    };
    /**
     * 根据当前位置更新选中的关卡
     */
    LevelSelectController.prototype.updateSelectedLevelByPosition = function () {
        if (!this.scrollView || !this.content)
            return;
        // 获取ScrollView的中心位置（相对于ScrollView节点）
        var scrollViewCenterX = 0; // ScrollView的中心就是x=0
        // 找到最接近ScrollView中心位置的关卡
        var closestLevel = 1;
        var minDistance = Number.MAX_VALUE;
        for (var i = 0; i < this.levelNodes.length; i++) {
            var levelNode = this.levelNodes[i];
            // 将关卡节点的本地坐标转换为ScrollView坐标系
            var levelWorldPos = this.content.convertToWorldSpaceAR(levelNode.position);
            var levelScrollViewPos = this.scrollView.node.convertToNodeSpaceAR(levelWorldPos);
            // 计算关卡与ScrollView中心的距离
            var distance = Math.abs(levelScrollViewPos.x - scrollViewCenterX);
            if (distance < minDistance) {
                minDistance = distance;
                closestLevel = i + 1;
            }
        }
        // 如果选中的关卡发生变化，更新显示
        if (closestLevel !== this.currentSelectedLevel) {
            this.currentSelectedLevel = closestLevel;
            this.updateLevelDisplay();
            // 通知关卡选择变化
            if (this.onLevelSelectionChanged) {
                this.onLevelSelectionChanged(closestLevel);
            }
        }
    };
    /**
     * 修复ScrollView的Scrollbar问题
     */
    LevelSelectController.prototype.fixScrollViewScrollbar = function () {
        if (this.scrollView && this.content) {
            ScrollViewHelper_1.ScrollViewHelper.setupHorizontalScrollView(this.scrollView, this.content);
            ScrollViewHelper_1.ScrollViewHelper.debugScrollView(this.scrollView, "LevelSelectController");
        }
    };
    /**
     * 调试参数信息
     */
    LevelSelectController.prototype.debugParameters = function () {
        // 计算连接线总宽度
        var totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        // 计算可用宽度
        var availableWidth = this.levelItemWidth - 46 - (this.levelToLineDistance * 2);
        if (totalLineWidth > availableWidth) {
            var adjustedSpacing = availableWidth / (this.lineCount - 1);
        }
        else {
        }
    };
    /**
     * 更新标签外边框
     */
    LevelSelectController.prototype.updateLabelOutline = function (outline, status) {
        var outlineColor;
        switch (status) {
            case LevelStatus.LOCKED:
                // 未解锁边框为 #7B7B7B
                outlineColor = cc.color(123, 123, 123);
                break;
            case LevelStatus.CURRENT:
                // 当前玩到的关卡边框 #CF5800
                outlineColor = cc.color(207, 88, 0);
                break;
            case LevelStatus.COMPLETED:
                // 已解锁边框为 #119C0F
                outlineColor = cc.color(17, 156, 15);
                break;
            default:
                outlineColor = cc.color(123, 123, 123);
                break;
        }
        outline.color = outlineColor;
        outline.width = 1;
    };
    /**
     * 重新创建关卡项目（用于参数调整后刷新）
     */
    LevelSelectController.prototype.refreshLevelItems = function () {
        this.createLevelItems();
        this.updateLevelDisplay();
        this.scrollToLevel(this.currentSelectedLevel);
    };
    __decorate([
        property(cc.ScrollView)
    ], LevelSelectController.prototype, "scrollView", void 0);
    __decorate([
        property(cc.Node)
    ], LevelSelectController.prototype, "content", void 0);
    LevelSelectController = __decorate([
        ccclass
    ], LevelSelectController);
    return LevelSelectController;
}(cc.Component));
exports.default = LevelSelectController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9zY3JpcHRzL2hhbGwvTGV2ZWwvTGV2ZWxTZWxlY3RDb250cm9sbGVyLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxvQkFBb0I7QUFDcEIsNEVBQTRFO0FBQzVFLG1CQUFtQjtBQUNuQixzRkFBc0Y7QUFDdEYsOEJBQThCO0FBQzlCLHNGQUFzRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUV0Rix1REFBc0Q7QUFFaEQsSUFBQSxLQUF3QixFQUFFLENBQUMsVUFBVSxFQUFuQyxPQUFPLGFBQUEsRUFBRSxRQUFRLGNBQWtCLENBQUM7QUFFNUMsU0FBUztBQUNULElBQVksV0FJWDtBQUpELFdBQVksV0FBVztJQUNuQixpREFBVSxDQUFBO0lBQ1YsbURBQVcsQ0FBQTtJQUNYLHVEQUFhLENBQUEsQ0FBRyxVQUFVO0FBQzlCLENBQUMsRUFKVyxXQUFXLEdBQVgsbUJBQVcsS0FBWCxtQkFBVyxRQUl0QjtBQVNEO0lBQW1ELHlDQUFZO0lBQS9EO1FBQUEscUVBNGxCQztRQXpsQkcsZ0JBQVUsR0FBa0IsSUFBSSxDQUFDO1FBR2pDLGFBQU8sR0FBWSxJQUFJLENBQUM7UUFFeEIsT0FBTztRQUNDLG1CQUFhLEdBQWdCLEVBQUUsQ0FBQztRQUNoQywwQkFBb0IsR0FBVyxDQUFDLENBQUM7UUFDakMsaUJBQVcsR0FBVyxFQUFFLENBQUM7UUFDekIsaUJBQVcsR0FBVyxHQUFHLENBQUM7UUFDMUIsb0JBQWMsR0FBVyxHQUFHLENBQUMsQ0FBQyxlQUFlO1FBQzdDLG1CQUFhLEdBQVcsQ0FBQyxDQUFDLENBQUMsU0FBUztRQUU1QyxpQkFBaUI7UUFDVixlQUFTLEdBQVcsQ0FBQyxDQUFDLENBQUMsZ0JBQWdCO1FBQ3ZDLGlCQUFXLEdBQVcsRUFBRSxDQUFDLENBQUMsV0FBVztRQUNyQyx5QkFBbUIsR0FBVyxDQUFDLENBQUMsQ0FBQyxZQUFZO1FBRXBELFNBQVM7UUFDRCxnQkFBVSxHQUFjLEVBQUUsQ0FBQztRQUVuQyxTQUFTO1FBQ0QscUJBQWUsR0FBWSxLQUFLLENBQUM7UUFFekMsV0FBVztRQUNKLDZCQUF1QixHQUFrQyxJQUFJLENBQUM7O0lBZ2tCekUsQ0FBQztJQTlqQkcsc0NBQU0sR0FBTjtRQUNJLDJCQUEyQjtRQUMzQixJQUFJLENBQUMsc0JBQXNCLEVBQUUsQ0FBQztRQUU5QixJQUFJLENBQUMsYUFBYSxFQUFFLENBQUM7UUFDckIsSUFBSSxDQUFDLGdCQUFnQixFQUFFLENBQUM7UUFDeEIsSUFBSSxDQUFDLGtCQUFrQixFQUFFLENBQUM7UUFDMUIsSUFBSSxDQUFDLGlCQUFpQixFQUFFLENBQUM7UUFFekIsb0JBQW9CO1FBQ3BCLElBQUksSUFBSSxDQUFDLFVBQVUsRUFBRTtZQUNqQixJQUFJLENBQUMsVUFBVSxDQUFDLHlCQUF5QixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztTQUNuRDtJQUNMLENBQUM7SUFFRCxxQ0FBSyxHQUFMO1FBQUEsaUJBTUM7UUFMRyxxQkFBcUI7UUFDckIsSUFBSSxDQUFDLFlBQVksQ0FBQztZQUNkLFdBQVc7WUFDWCxLQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQzFCLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQztJQUNaLENBQUM7SUFFRCx5Q0FBUyxHQUFUO1FBQ0ksVUFBVTtRQUNWLElBQUksSUFBSSxDQUFDLFVBQVUsRUFBRTtZQUNqQixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsV0FBVyxFQUFFLElBQUksQ0FBQyxXQUFXLEVBQUUsSUFBSSxDQUFDLENBQUM7WUFDOUQsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLGNBQWMsRUFBRSxJQUFJLENBQUMsYUFBYSxFQUFFLElBQUksQ0FBQyxDQUFDO1NBQ3RFO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0ssNkNBQWEsR0FBckI7UUFDSSxJQUFJLENBQUMsYUFBYSxHQUFHLEVBQUUsQ0FBQztRQUV4Qiw2QkFBNkI7UUFDN0IsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxJQUFJLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDeEMsSUFBSSxNQUFNLFNBQWEsQ0FBQztZQUN4QixJQUFJLENBQUMsS0FBSyxDQUFDLEVBQUU7Z0JBQ1QsTUFBTSxHQUFHLFdBQVcsQ0FBQyxPQUFPLENBQUMsQ0FBQyx5QkFBeUI7YUFDMUQ7aUJBQU07Z0JBQ0gsTUFBTSxHQUFHLFdBQVcsQ0FBQyxNQUFNLENBQUMsQ0FBQyxTQUFTO2FBQ3pDO1lBRUQsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUM7Z0JBQ3BCLFdBQVcsRUFBRSxDQUFDO2dCQUNkLE1BQU0sRUFBRSxNQUFNO2FBQ2pCLENBQUMsQ0FBQztTQUNOO1FBRUQsVUFBVTtRQUNWLElBQUksQ0FBQyxvQkFBb0IsR0FBRyxDQUFDLENBQUM7SUFDbEMsQ0FBQztJQUVEOztPQUVHO0lBQ0ssZ0RBQWdCLEdBQXhCO1FBQ0ksSUFBSSxDQUFDLElBQUksQ0FBQyxPQUFPLEVBQUU7WUFDZixFQUFFLENBQUMsS0FBSyxDQUFDLCtCQUErQixDQUFDLENBQUM7WUFDMUMsT0FBTztTQUNWO1FBRUQsU0FBUztRQUNULElBQUksQ0FBQyxPQUFPLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztRQUNqQyxJQUFJLENBQUMsVUFBVSxHQUFHLEVBQUUsQ0FBQztRQUVyQixRQUFRO1FBQ1IsSUFBTSxVQUFVLEdBQUcsQ0FBQyxJQUFJLENBQUMsV0FBVyxHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxjQUFjLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQztRQUNuRixJQUFJLENBQUMsT0FBTyxDQUFDLEtBQUssR0FBRyxVQUFVLENBQUM7UUFFaEMsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDdkMsSUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUV4QyxTQUFTO1lBQ1QsSUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLGVBQWUsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUNsRCxJQUFJLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUNqQyxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUVoQyxPQUFPO1lBQ1AsSUFBTSxJQUFJLEdBQUcsQ0FBQyxHQUFHLElBQUksQ0FBQyxjQUFjLEdBQUcsVUFBVSxHQUFHLENBQUMsR0FBRyxJQUFJLENBQUMsV0FBVyxHQUFHLENBQUMsQ0FBQztZQUM3RSxTQUFTLENBQUMsV0FBVyxDQUFDLElBQUksRUFBRSxDQUFDLENBQUMsQ0FBQztZQUUvQixrQkFBa0I7WUFDbEIsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLFdBQVcsR0FBRyxDQUFDLEVBQUU7Z0JBQzFCLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDLEVBQUUsSUFBSSxDQUFDLENBQUM7YUFDdkM7U0FDSjtJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNLLCtDQUFlLEdBQXZCLFVBQXdCLFNBQW9CO1FBQ3hDLElBQU0sSUFBSSxHQUFHLElBQUksRUFBRSxDQUFDLElBQUksQ0FBQyxXQUFTLFNBQVMsQ0FBQyxXQUFhLENBQUMsQ0FBQztRQUUzRCxhQUFhO1FBQ2IsSUFBTSxNQUFNLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUM7UUFFNUMsa0JBQWtCO1FBQ2xCLElBQU0sU0FBUyxHQUFHLElBQUksRUFBRSxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztRQUM1QyxJQUFNLEtBQUssR0FBRyxTQUFTLENBQUMsWUFBWSxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUMvQyxLQUFLLENBQUMsTUFBTSxHQUFHLFNBQVMsQ0FBQyxXQUFXLENBQUMsUUFBUSxFQUFFLENBQUM7UUFFaEQsU0FBUztRQUNULEtBQUssQ0FBQyxRQUFRLEdBQUcsRUFBRSxDQUFDO1FBQ3BCLEtBQUssQ0FBQyxJQUFJLENBQUMsS0FBSyxHQUFHLEVBQUUsQ0FBQyxLQUFLLENBQUMsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLENBQUMsQ0FBQyxDQUFDLFVBQVU7UUFDdEQsS0FBSyxDQUFDLGVBQWUsR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLGVBQWUsQ0FBQyxNQUFNLENBQUM7UUFDeEQsS0FBSyxDQUFDLGFBQWEsR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLGFBQWEsQ0FBQyxNQUFNLENBQUM7UUFFcEQsUUFBUTtRQUNSLElBQU0sT0FBTyxHQUFHLEtBQUssQ0FBQyxZQUFZLENBQUMsRUFBRSxDQUFDLFlBQVksQ0FBQyxDQUFDO1FBQ3BELElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxPQUFPLEVBQUUsU0FBUyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBRW5ELFNBQVMsQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDO1FBQ3hCLFNBQVMsQ0FBQyxXQUFXLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsT0FBTztRQUVwQyxhQUFhO1FBQ2IsSUFBTSxNQUFNLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUM7UUFDNUMsTUFBTSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUM7UUFFckIsU0FBUztRQUNULElBQU0sWUFBWSxHQUFHLElBQUksRUFBRSxDQUFDLFNBQVMsQ0FBQyxZQUFZLEVBQUUsQ0FBQztRQUNyRCxZQUFZLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUM7UUFDaEMsWUFBWSxDQUFDLFNBQVMsR0FBRyx1QkFBdUIsQ0FBQztRQUNqRCxZQUFZLENBQUMsT0FBTyxHQUFHLGdCQUFnQixDQUFDO1FBQ3hDLFlBQVksQ0FBQyxlQUFlLEdBQUcsU0FBUyxDQUFDLFdBQVcsQ0FBQyxRQUFRLEVBQUUsQ0FBQztRQUNoRSxNQUFNLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztRQUV0QyxTQUFTO1FBQ1QsSUFBSSxDQUFDLHlCQUF5QixDQUFDLElBQUksRUFBRSxTQUFTLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFFdkQsT0FBTyxJQUFJLENBQUM7SUFDaEIsQ0FBQztJQUVEOztPQUVHO0lBQ0sscURBQXFCLEdBQTdCLFVBQThCLFVBQWtCLEVBQUUsU0FBaUI7UUFDL0QsSUFBTSxNQUFNLEdBQUcsU0FBUyxHQUFHLEVBQUUsR0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLG1CQUFtQixDQUFDLENBQUMsYUFBYTtRQUN6RSxJQUFNLGNBQWMsR0FBRyxDQUFDLElBQUksQ0FBQyxTQUFTLEdBQUcsQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQztRQUMvRCxJQUFNLElBQUksR0FBRyxTQUFTLEdBQUcsSUFBSSxDQUFDLGNBQWMsR0FBRyxFQUFFLEdBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDLGdCQUFnQjtRQUNoRyxJQUFNLGNBQWMsR0FBRyxJQUFJLEdBQUcsTUFBTSxDQUFDO1FBRXJDLHFCQUFxQjtRQUNyQixJQUFJLGFBQWEsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDO1FBQ3JDLElBQUksY0FBYyxHQUFHLGNBQWMsRUFBRTtZQUNqQyxhQUFhLEdBQUcsY0FBYyxHQUFHLENBQUMsSUFBSSxDQUFDLFNBQVMsR0FBRyxDQUFDLENBQUMsQ0FBQztTQUN6RDtRQUVELEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUMsU0FBUyxFQUFFLENBQUMsRUFBRSxFQUFFO1lBQ3JDLElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxvQkFBb0IsRUFBRSxDQUFDO1lBQzdDLElBQUksQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBRWhDLElBQU0sS0FBSyxHQUFHLE1BQU0sR0FBRyxDQUFDLEdBQUcsYUFBYSxDQUFDO1lBQ3pDLFFBQVEsQ0FBQyxXQUFXLENBQUMsS0FBSyxFQUFFLENBQUMsQ0FBQyxDQUFDO1NBQ2xDO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0ssb0RBQW9CLEdBQTVCO1FBQ0ksSUFBTSxJQUFJLEdBQUcsSUFBSSxFQUFFLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQ2pDLElBQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBRTVDLGNBQWM7UUFDZCxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUUxQixVQUFVO1FBQ1YsRUFBRSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsa0NBQWtDLEVBQUUsRUFBRSxDQUFDLFdBQVcsRUFBRSxVQUFDLEdBQUcsRUFBRSxXQUFXO1lBQ25GLElBQUksQ0FBQyxHQUFHLElBQUksV0FBVyxFQUFFO2dCQUNyQixNQUFNLENBQUMsV0FBVyxHQUFHLFdBQTZCLENBQUM7Z0JBQ25ELCtCQUErQjtnQkFDL0IsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7YUFDN0I7UUFDTCxDQUFDLENBQUMsQ0FBQztRQUVILE9BQU8sSUFBSSxDQUFDO0lBQ2hCLENBQUM7SUFFRDs7T0FFRztJQUNLLDhDQUFjLEdBQXRCLFVBQXVCLFdBQW1CO1FBQ3RDLE9BQU8sV0FBVyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUM7SUFDakMsQ0FBQztJQUVEOztPQUVHO0lBQ0sseURBQXlCLEdBQWpDLFVBQWtDLElBQWEsRUFBRSxTQUFvQixFQUFFLFVBQW1CO1FBQ3RGLElBQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQzVDLElBQUksQ0FBQyxNQUFNO1lBQUUsT0FBTztRQUVwQixJQUFJLFNBQVMsR0FBRyxFQUFFLENBQUM7UUFDbkIsSUFBSSxJQUFJLEdBQUcsRUFBRSxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUUsRUFBRSxDQUFDLENBQUM7UUFFM0IsNkJBQTZCO1FBQzdCLElBQU0sY0FBYyxHQUFHLElBQUksQ0FBQyxjQUFjLENBQUMsU0FBUyxDQUFDLFdBQVcsQ0FBQyxDQUFDO1FBQ2xFLElBQU0sUUFBUSxHQUFHLGNBQWMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUM7UUFHNUMsa0JBQWtCO1FBQ2xCLElBQUksVUFBVSxFQUFFO1lBQ1osSUFBSSxHQUFHLEVBQUUsQ0FBQyxJQUFJLENBQUMsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDO1lBQ3ZCLFFBQVEsU0FBUyxDQUFDLE1BQU0sRUFBRTtnQkFDdEIsS0FBSyxXQUFXLENBQUMsTUFBTTtvQkFDbkIsU0FBUyxHQUFHLHFDQUFtQyxRQUFRLFlBQVMsQ0FBQztvQkFDakUsTUFBTTtnQkFDVixLQUFLLFdBQVcsQ0FBQyxPQUFPO29CQUNwQixTQUFTLEdBQUcsdUNBQXFDLFFBQVEsWUFBUyxDQUFDO29CQUNuRSxNQUFNO2dCQUNWLEtBQUssV0FBVyxDQUFDLFNBQVM7b0JBQ3RCLFNBQVMsR0FBRyxzQ0FBb0MsUUFBUSxZQUFTLENBQUM7b0JBQ2xFLE1BQU07YUFDYjtTQUNKO2FBQU07WUFDSCxRQUFRLFNBQVMsQ0FBQyxNQUFNLEVBQUU7Z0JBQ3RCLEtBQUssV0FBVyxDQUFDLE1BQU07b0JBQ25CLFNBQVMsR0FBRyxxQ0FBbUMsUUFBVSxDQUFDO29CQUMxRCxNQUFNO2dCQUNWLEtBQUssV0FBVyxDQUFDLE9BQU87b0JBQ3BCLFNBQVMsR0FBRyx1Q0FBcUMsUUFBVSxDQUFDO29CQUM1RCxNQUFNO2dCQUNWLEtBQUssV0FBVyxDQUFDLFNBQVM7b0JBQ3RCLFNBQVMsR0FBRyxzQ0FBb0MsUUFBVSxDQUFDO29CQUMzRCxNQUFNO2FBQ2I7U0FDSjtRQUVELFNBQVM7UUFDVCxJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxDQUFDO1FBRTFCLFVBQVU7UUFDVixFQUFFLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxTQUFTLEVBQUUsRUFBRSxDQUFDLFdBQVcsRUFBRSxVQUFDLEdBQUcsRUFBRSxXQUFXO1lBQzFELElBQUksQ0FBQyxHQUFHLElBQUksV0FBVyxFQUFFO2dCQUNyQixNQUFNLENBQUMsV0FBVyxHQUFHLFdBQTZCLENBQUM7Z0JBQ25ELDBCQUEwQjtnQkFDMUIsSUFBSSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsQ0FBQzthQUM3QjtRQUNMLENBQUMsQ0FBQyxDQUFDO1FBRUgsVUFBVTtRQUNWLElBQU0sU0FBUyxHQUFHLElBQUksQ0FBQyxjQUFjLENBQUMsWUFBWSxDQUFDLENBQUM7UUFDcEQsSUFBSSxTQUFTLEVBQUU7WUFDWCxJQUFNLEtBQUssR0FBRyxTQUFTLENBQUMsWUFBWSxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUMvQyxJQUFJLEtBQUssRUFBRTtnQkFDUCxJQUFNLE9BQU8sR0FBRyxLQUFLLENBQUMsWUFBWSxDQUFDLEVBQUUsQ0FBQyxZQUFZLENBQUMsQ0FBQztnQkFDcEQsSUFBSSxPQUFPLEVBQUU7b0JBQ1QsSUFBSSxDQUFDLGtCQUFrQixDQUFDLE9BQU8sRUFBRSxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUM7aUJBQ3REO2FBQ0o7U0FDSjtJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNLLGtEQUFrQixHQUExQjtRQUNJLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRTtZQUM3QyxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2hDLElBQU0sU0FBUyxHQUFHLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDeEMsSUFBTSxVQUFVLEdBQUcsQ0FBQyxTQUFTLENBQUMsV0FBVyxLQUFLLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO1lBRXpFLElBQUksQ0FBQyx5QkFBeUIsQ0FBQyxJQUFJLEVBQUUsU0FBUyxFQUFFLFVBQVUsQ0FBQyxDQUFDO1NBQy9EO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0ssNkNBQWEsR0FBckIsVUFBc0IsV0FBbUI7UUFDckMsSUFBSSxXQUFXLEdBQUcsQ0FBQyxJQUFJLFdBQVcsR0FBRyxJQUFJLENBQUMsV0FBVztZQUFFLE9BQU87UUFFOUQsSUFBTSxXQUFXLEdBQUcsV0FBVyxHQUFHLENBQUMsQ0FBQztRQUNwQyxJQUFNLFlBQVksR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQztRQUN4QyxJQUFNLGVBQWUsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUM7UUFFbkQsYUFBYTtRQUNiLElBQU0sWUFBWSxHQUFHLENBQUMsV0FBVyxHQUFHLElBQUksQ0FBQyxjQUFjLENBQUMsR0FBRyxDQUFDLFlBQVksR0FBRyxlQUFlLENBQUMsQ0FBQztRQUU1RixjQUFjO1FBQ2QsSUFBTSxhQUFhLEdBQUcsRUFBRSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsWUFBWSxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUV6RCxTQUFTO1FBQ1QsSUFBSSxDQUFDLFVBQVUsQ0FBQyx5QkFBeUIsQ0FBQyxhQUFhLEVBQUUsR0FBRyxDQUFDLENBQUM7SUFDbEUsQ0FBQztJQUVEOztPQUVHO0lBQ0ksOENBQWMsR0FBckIsVUFBc0IsS0FBZSxFQUFFLGVBQXVCO1FBQzFELElBQU0sV0FBVyxHQUFHLFFBQVEsQ0FBQyxlQUFlLENBQUMsQ0FBQztRQUU5QyxtQkFBbUI7UUFDbkIsV0FBVztRQUNYLElBQUksQ0FBQyxvQkFBb0IsR0FBRyxXQUFXLENBQUM7UUFDeEMsSUFBSSxDQUFDLGtCQUFrQixFQUFFLENBQUM7UUFFMUIsVUFBVTtRQUNWLElBQUksQ0FBQyxlQUFlLEdBQUcsSUFBSSxDQUFDO1FBQzVCLElBQUksQ0FBQyxhQUFhLENBQUMsV0FBVyxDQUFDLENBQUM7UUFJaEMsV0FBVztRQUNYLElBQUksSUFBSSxDQUFDLHVCQUF1QixFQUFFO1lBQzlCLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxXQUFXLENBQUMsQ0FBQztTQUM3QztRQUVELGdCQUFnQjtRQUNoQixnQ0FBZ0M7SUFDcEMsQ0FBQztJQUVEOztPQUVHO0lBQ0ksOENBQWMsR0FBckIsVUFBc0IsV0FBbUIsRUFBRSxNQUFtQjtRQUMxRCxJQUFJLFdBQVcsR0FBRyxDQUFDLElBQUksV0FBVyxHQUFHLElBQUksQ0FBQyxXQUFXO1lBQUUsT0FBTztRQUU5RCxJQUFJLENBQUMsYUFBYSxDQUFDLFdBQVcsR0FBRyxDQUFDLENBQUMsQ0FBQyxNQUFNLEdBQUcsTUFBTSxDQUFDO1FBQ3BELElBQUksQ0FBQyxrQkFBa0IsRUFBRSxDQUFDO0lBQzlCLENBQUM7SUFFRDs7T0FFRztJQUNJLHVEQUF1QixHQUE5QjtRQUNJLE9BQU8sSUFBSSxDQUFDLG9CQUFvQixDQUFDO0lBQ3JDLENBQUM7SUFFRDs7T0FFRztJQUNJLDRDQUFZLEdBQW5CLFVBQW9CLFdBQW1CO1FBQ25DLElBQUksV0FBVyxHQUFHLENBQUMsSUFBSSxXQUFXLEdBQUcsSUFBSSxDQUFDLFdBQVc7WUFBRSxPQUFPLElBQUksQ0FBQztRQUNuRSxPQUFPLElBQUksQ0FBQyxhQUFhLENBQUMsV0FBVyxHQUFHLENBQUMsQ0FBQyxDQUFDO0lBQy9DLENBQUM7SUFFRDs7O09BR0c7SUFDSSxnREFBZ0IsR0FBdkIsVUFBd0IsaUJBQXNCO1FBQzFDLElBQUksQ0FBQyxpQkFBaUI7WUFBRSxPQUFPO1FBRXZCLElBQUEsYUFBYSxHQUFnQyxpQkFBaUIsY0FBakQsRUFBRSxZQUFZLEdBQWtCLGlCQUFpQixhQUFuQyxFQUFFLFdBQVcsR0FBSyxpQkFBaUIsWUFBdEIsQ0FBdUI7UUFFdkUsVUFBVTtRQUNWLElBQUksYUFBYSxHQUFHLENBQUMsSUFBSSxZQUFZLEdBQUcsQ0FBQyxJQUFJLFlBQVksR0FBRyxXQUFXO1lBQUUsT0FBTztRQUVoRixtQkFBbUI7UUFDbkIsSUFBSSxXQUFXLElBQUksV0FBVyxHQUFHLENBQUMsRUFBRTtZQUNoQyxJQUFJLENBQUMsV0FBVyxHQUFHLFdBQVcsQ0FBQztTQUNsQztRQUVELGFBQWE7UUFDYixLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLElBQUksSUFBSSxDQUFDLFdBQVcsRUFBRSxDQUFDLEVBQUUsRUFBRTtZQUN4QyxJQUFJLE1BQU0sU0FBYSxDQUFDO1lBQ3hCLElBQUksQ0FBQyxHQUFHLFlBQVksRUFBRTtnQkFDbEIsTUFBTSxHQUFHLFdBQVcsQ0FBQyxTQUFTLENBQUMsQ0FBQyxtQkFBbUI7YUFDdEQ7aUJBQU0sSUFBSSxDQUFDLEtBQUssWUFBWSxFQUFFO2dCQUMzQixNQUFNLEdBQUcsV0FBVyxDQUFDLE9BQU8sQ0FBQyxDQUFDLGVBQWU7YUFDaEQ7aUJBQU07Z0JBQ0gsTUFBTSxHQUFHLFdBQVcsQ0FBQyxNQUFNLENBQUMsQ0FBQyxtQkFBbUI7YUFDbkQ7WUFFRCxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxNQUFNLEdBQUcsTUFBTSxDQUFDO1NBQzdDO1FBRUQsNkJBQTZCO1FBQzdCLElBQUksQ0FBQyxvQkFBb0IsR0FBRyxZQUFZLENBQUM7UUFDekMsSUFBSSxDQUFDLGtCQUFrQixFQUFFLENBQUM7UUFDMUIsSUFBSSxDQUFDLGFBQWEsQ0FBQyxZQUFZLENBQUMsQ0FBQztRQUVqQyxXQUFXO1FBQ1gsSUFBSSxJQUFJLENBQUMsdUJBQXVCLEVBQUU7WUFDOUIsSUFBSSxDQUFDLHVCQUF1QixDQUFDLFlBQVksQ0FBQyxDQUFDO1NBQzlDO0lBQ0wsQ0FBQztJQUVEOzs7T0FHRztJQUNJLHNEQUFzQixHQUE3QixVQUE4QixlQUF1QjtRQUNqRCxZQUFZO1FBQ1osSUFBTSxpQkFBaUIsR0FBRztZQUN0QixhQUFhLEVBQUUsZUFBZTtZQUM5QixZQUFZLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxlQUFlLEdBQUcsQ0FBQyxFQUFFLElBQUksQ0FBQyxXQUFXLENBQUM7WUFDN0QsV0FBVyxFQUFFLElBQUksQ0FBQyxXQUFXO1NBQ2hDLENBQUM7UUFDRixJQUFJLENBQUMsZ0JBQWdCLENBQUMsaUJBQWlCLENBQUMsQ0FBQztJQUM3QyxDQUFDO0lBRUQ7O09BRUc7SUFDSSwrQ0FBZSxHQUF0QjtRQUNJLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUMsYUFBYSxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRTtZQUNoRCxJQUFJLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxLQUFLLFdBQVcsQ0FBQyxNQUFNLEVBQUU7Z0JBQ3JELElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxHQUFHLFdBQVcsQ0FBQyxPQUFPLENBQUM7Z0JBQ25ELElBQUksQ0FBQyxrQkFBa0IsRUFBRSxDQUFDO2dCQUMxQixNQUFNO2FBQ1Q7U0FDSjtJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNJLG9EQUFvQixHQUEzQjtRQUNJLElBQU0sZ0JBQWdCLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsb0JBQW9CLEdBQUcsQ0FBQyxDQUFDLENBQUM7UUFDM0UsSUFBSSxnQkFBZ0IsQ0FBQyxNQUFNLEtBQUssV0FBVyxDQUFDLE9BQU8sRUFBRTtZQUNqRCxnQkFBZ0IsQ0FBQyxNQUFNLEdBQUcsV0FBVyxDQUFDLFNBQVMsQ0FBQztZQUNoRCxJQUFJLENBQUMsZUFBZSxFQUFFLENBQUM7WUFDdkIsSUFBSSxDQUFDLGtCQUFrQixFQUFFLENBQUM7U0FDN0I7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSyxpREFBaUIsR0FBekI7UUFDSSxJQUFJLENBQUMsSUFBSSxDQUFDLFVBQVU7WUFBRSxPQUFPO1FBRTdCLFVBQVU7UUFDVixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsV0FBVyxFQUFFLElBQUksQ0FBQyxXQUFXLEVBQUUsSUFBSSxDQUFDLENBQUM7UUFFN0QsV0FBVztRQUNYLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxjQUFjLEVBQUUsSUFBSSxDQUFDLGFBQWEsRUFBRSxJQUFJLENBQUMsQ0FBQztJQUd0RSxDQUFDO0lBRUQ7O09BRUc7SUFDSywyQ0FBVyxHQUFuQjtRQUNJLElBQUksQ0FBQyw2QkFBNkIsRUFBRSxDQUFDO0lBQ3pDLENBQUM7SUFFRDs7T0FFRztJQUNLLDZDQUFhLEdBQXJCO1FBQ0ksa0JBQWtCO1FBQ2xCLElBQUksSUFBSSxDQUFDLGVBQWUsRUFBRTtZQUN0QixJQUFJLENBQUMsZUFBZSxHQUFHLEtBQUssQ0FBQztZQUM3QixPQUFPO1NBQ1Y7UUFFRCxJQUFJLENBQUMsNkJBQTZCLEVBQUUsQ0FBQztRQUNyQyxtQkFBbUI7UUFDbkIsSUFBSSxDQUFDLGVBQWUsR0FBRyxJQUFJLENBQUM7UUFDNUIsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsb0JBQW9CLENBQUMsQ0FBQztJQUNsRCxDQUFDO0lBRUQ7O09BRUc7SUFDSyw2REFBNkIsR0FBckM7UUFDSSxJQUFJLENBQUMsSUFBSSxDQUFDLFVBQVUsSUFBSSxDQUFDLElBQUksQ0FBQyxPQUFPO1lBQUUsT0FBTztRQUU5QyxxQ0FBcUM7UUFDckMsSUFBTSxpQkFBaUIsR0FBRyxDQUFDLENBQUMsQ0FBQyxxQkFBcUI7UUFFbEQseUJBQXlCO1FBQ3pCLElBQUksWUFBWSxHQUFHLENBQUMsQ0FBQztRQUNyQixJQUFJLFdBQVcsR0FBRyxNQUFNLENBQUMsU0FBUyxDQUFDO1FBRW5DLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRTtZQUM3QyxJQUFNLFNBQVMsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBRXJDLDZCQUE2QjtZQUM3QixJQUFNLGFBQWEsR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLHFCQUFxQixDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUM3RSxJQUFNLGtCQUFrQixHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLG9CQUFvQixDQUFDLGFBQWEsQ0FBQyxDQUFDO1lBRXBGLHVCQUF1QjtZQUN2QixJQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLGtCQUFrQixDQUFDLENBQUMsR0FBRyxpQkFBaUIsQ0FBQyxDQUFDO1lBRXBFLElBQUksUUFBUSxHQUFHLFdBQVcsRUFBRTtnQkFDeEIsV0FBVyxHQUFHLFFBQVEsQ0FBQztnQkFDdkIsWUFBWSxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUM7YUFDeEI7U0FDSjtRQUVELG1CQUFtQjtRQUNuQixJQUFJLFlBQVksS0FBSyxJQUFJLENBQUMsb0JBQW9CLEVBQUU7WUFDNUMsSUFBSSxDQUFDLG9CQUFvQixHQUFHLFlBQVksQ0FBQztZQUN6QyxJQUFJLENBQUMsa0JBQWtCLEVBQUUsQ0FBQztZQUcxQixXQUFXO1lBQ1gsSUFBSSxJQUFJLENBQUMsdUJBQXVCLEVBQUU7Z0JBQzlCLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxZQUFZLENBQUMsQ0FBQzthQUM5QztTQUNKO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0ssc0RBQXNCLEdBQTlCO1FBQ0ksSUFBSSxJQUFJLENBQUMsVUFBVSxJQUFJLElBQUksQ0FBQyxPQUFPLEVBQUU7WUFDakMsbUNBQWdCLENBQUMseUJBQXlCLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRSxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUM7WUFDMUUsbUNBQWdCLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsdUJBQXVCLENBQUMsQ0FBQztTQUM5RTtJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNJLCtDQUFlLEdBQXRCO1FBR0ksV0FBVztRQUNYLElBQU0sY0FBYyxHQUFHLENBQUMsSUFBSSxDQUFDLFNBQVMsR0FBRyxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDO1FBRy9ELFNBQVM7UUFDVCxJQUFNLGNBQWMsR0FBRyxJQUFJLENBQUMsY0FBYyxHQUFHLEVBQUUsR0FBRyxDQUFDLElBQUksQ0FBQyxtQkFBbUIsR0FBRyxDQUFDLENBQUMsQ0FBQztRQUdqRixJQUFJLGNBQWMsR0FBRyxjQUFjLEVBQUU7WUFDakMsSUFBTSxlQUFlLEdBQUcsY0FBYyxHQUFHLENBQUMsSUFBSSxDQUFDLFNBQVMsR0FBRyxDQUFDLENBQUMsQ0FBQztTQUVqRTthQUFNO1NBRU47SUFHTCxDQUFDO0lBRUQ7O09BRUc7SUFDSyxrREFBa0IsR0FBMUIsVUFBMkIsT0FBd0IsRUFBRSxNQUFtQjtRQUNwRSxJQUFJLFlBQXNCLENBQUM7UUFDM0IsUUFBUSxNQUFNLEVBQUU7WUFDWixLQUFLLFdBQVcsQ0FBQyxNQUFNO2dCQUNuQixpQkFBaUI7Z0JBQ2pCLFlBQVksR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxDQUFDLENBQUM7Z0JBQ3ZDLE1BQU07WUFDVixLQUFLLFdBQVcsQ0FBQyxPQUFPO2dCQUNwQixvQkFBb0I7Z0JBQ3BCLFlBQVksR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLEdBQUcsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUM7Z0JBQ3BDLE1BQU07WUFDVixLQUFLLFdBQVcsQ0FBQyxTQUFTO2dCQUN0QixpQkFBaUI7Z0JBQ2pCLFlBQVksR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLEVBQUUsRUFBRSxHQUFHLEVBQUUsRUFBRSxDQUFDLENBQUM7Z0JBQ3JDLE1BQU07WUFDVjtnQkFDSSxZQUFZLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsQ0FBQyxDQUFDO2dCQUN2QyxNQUFNO1NBQ2I7UUFFRCxPQUFPLENBQUMsS0FBSyxHQUFHLFlBQVksQ0FBQztRQUM3QixPQUFPLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQztJQUN0QixDQUFDO0lBRUQ7O09BRUc7SUFDSSxpREFBaUIsR0FBeEI7UUFDSSxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQztRQUN4QixJQUFJLENBQUMsa0JBQWtCLEVBQUUsQ0FBQztRQUMxQixJQUFJLENBQUMsYUFBYSxDQUFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO0lBQ2xELENBQUM7SUF0bEJEO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxVQUFVLENBQUM7NkRBQ1M7SUFHakM7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzswREFDTTtJQU5QLHFCQUFxQjtRQUR6QyxPQUFPO09BQ2EscUJBQXFCLENBNGxCekM7SUFBRCw0QkFBQztDQTVsQkQsQUE0bEJDLENBNWxCa0QsRUFBRSxDQUFDLFNBQVMsR0E0bEI5RDtrQkE1bEJvQixxQkFBcUIiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyIvLyBMZWFybiBUeXBlU2NyaXB0OlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL3R5cGVzY3JpcHQuaHRtbFxuLy8gTGVhcm4gQXR0cmlidXRlOlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL3JlZmVyZW5jZS9hdHRyaWJ1dGVzLmh0bWxcbi8vIExlYXJuIGxpZmUtY3ljbGUgY2FsbGJhY2tzOlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL2xpZmUtY3ljbGUtY2FsbGJhY2tzLmh0bWxcblxuaW1wb3J0IHsgU2Nyb2xsVmlld0hlbHBlciB9IGZyb20gXCIuL1Njcm9sbFZpZXdIZWxwZXJcIjtcblxuY29uc3QgeyBjY2NsYXNzLCBwcm9wZXJ0eSB9ID0gY2MuX2RlY29yYXRvcjtcblxuLy8g5YWz5Y2h54q25oCB5p6a5Li+XG5leHBvcnQgZW51bSBMZXZlbFN0YXR1cyB7XG4gICAgTE9DS0VEID0gMCwgICAgIC8vIOacquino+mUge+8iOeBsOiJsu+8iVxuICAgIENVUlJFTlQgPSAxLCAgICAvLyDmraPlnKjov5vooYzvvIjpu4ToibLvvIlcbiAgICBDT01QTEVURUQgPSAyICAgLy8g5bey6YCa5YWz77yI57u/6Imy77yJXG59XG5cbi8vIOWFs+WNoeaVsOaNruaOpeWPo1xuZXhwb3J0IGludGVyZmFjZSBMZXZlbERhdGEge1xuICAgIGxldmVsTnVtYmVyOiBudW1iZXI7XG4gICAgc3RhdHVzOiBMZXZlbFN0YXR1cztcbn1cblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIExldmVsU2VsZWN0Q29udHJvbGxlciBleHRlbmRzIGNjLkNvbXBvbmVudCB7XG5cbiAgICBAcHJvcGVydHkoY2MuU2Nyb2xsVmlldylcbiAgICBzY3JvbGxWaWV3OiBjYy5TY3JvbGxWaWV3ID0gbnVsbDtcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIGNvbnRlbnQ6IGNjLk5vZGUgPSBudWxsO1xuXG4gICAgLy8g5YWz5Y2h5pWw5o2uXG4gICAgcHJpdmF0ZSBsZXZlbERhdGFMaXN0OiBMZXZlbERhdGFbXSA9IFtdO1xuICAgIHByaXZhdGUgY3VycmVudFNlbGVjdGVkTGV2ZWw6IG51bWJlciA9IDE7XG4gICAgcHJpdmF0ZSB0b3RhbExldmVsczogbnVtYmVyID0gMzA7XG4gICAgcHJpdmF0ZSBzY3JlZW5XaWR0aDogbnVtYmVyID0gNjUwO1xuICAgIHByaXZhdGUgbGV2ZWxJdGVtV2lkdGg6IG51bWJlciA9IDE1MDsgLy8g5YWz5Y2h6aG555qE5a695bqm77yI5YyF5ous6Ze06Led77yJXG4gICAgcHJpdmF0ZSB2aXNpYmxlTGV2ZWxzOiBudW1iZXIgPSAzOyAvLyDlj6/op4HlhbPljaHmlbDph49cblxuICAgIC8vIOi/nuaOpee6v+mFjee9ru+8iOWFrOW8gO+8jOaWueS+v+iwg+ivle+8iVxuICAgIHB1YmxpYyBsaW5lQ291bnQ6IG51bWJlciA9IDk7IC8vIOavj+S4pOS4quWFs+WNoeS5i+mXtOeahOi/nuaOpee6v+aVsOmHj1xuICAgIHB1YmxpYyBsaW5lU3BhY2luZzogbnVtYmVyID0gMTY7IC8vIOi/nuaOpee6v+S5i+mXtOeahOmXtOi3nVxuICAgIHB1YmxpYyBsZXZlbFRvTGluZURpc3RhbmNlOiBudW1iZXIgPSA4OyAvLyDlhbPljaHliLDov57mjqXnur/nmoTot53nprtcblxuICAgIC8vIOWFs+WNoeiKgueCueWIl+ihqFxuICAgIHByaXZhdGUgbGV2ZWxOb2RlczogY2MuTm9kZVtdID0gW107XG5cbiAgICAvLyDmu5HliqjnirbmgIHmoIflv5dcbiAgICBwcml2YXRlIGlzQXV0b1Njcm9sbGluZzogYm9vbGVhbiA9IGZhbHNlO1xuXG4gICAgLy8g5YWz5Y2h6YCJ5oup5Y+Y5YyW5Zue6LCDXG4gICAgcHVibGljIG9uTGV2ZWxTZWxlY3Rpb25DaGFuZ2VkOiAobGV2ZWxOdW1iZXI6IG51bWJlcikgPT4gdm9pZCA9IG51bGw7XG5cbiAgICBvbkxvYWQoKSB7XG4gICAgICAgIC8vIOS/ruWkjVNjcm9sbFZpZXfnmoRTY3JvbGxiYXLpl67pophcbiAgICAgICAgdGhpcy5maXhTY3JvbGxWaWV3U2Nyb2xsYmFyKCk7XG5cbiAgICAgICAgdGhpcy5pbml0TGV2ZWxEYXRhKCk7XG4gICAgICAgIHRoaXMuY3JlYXRlTGV2ZWxJdGVtcygpO1xuICAgICAgICB0aGlzLnVwZGF0ZUxldmVsRGlzcGxheSgpO1xuICAgICAgICB0aGlzLnNldHVwU2Nyb2xsRXZlbnRzKCk7XG5cbiAgICAgICAgLy8g6K6+572u5Yid5aeL5rua5Yqo5L2N572u5Li656ysMeWFs++8iOacgOW3pui+ue+8iVxuICAgICAgICBpZiAodGhpcy5zY3JvbGxWaWV3KSB7XG4gICAgICAgICAgICB0aGlzLnNjcm9sbFZpZXcuc2Nyb2xsVG9QZXJjZW50SG9yaXpvbnRhbCgwLCAwKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIHN0YXJ0KCkge1xuICAgICAgICAvLyDlu7bov5/mu5rliqjliLDnrKzkuIDlhbPvvIznoa7kv53nlYzpnaLlrozlhajliJ3lp4vljJZcbiAgICAgICAgdGhpcy5zY2hlZHVsZU9uY2UoKCkgPT4ge1xuICAgICAgICAgICAgLy8g56Gu5L+d5rua5Yqo5Yiw56ysMeWFs1xuICAgICAgICAgICAgdGhpcy5zY3JvbGxUb0xldmVsKDEpO1xuICAgICAgICB9LCAwLjEpO1xuICAgIH1cblxuICAgIG9uRGVzdHJveSgpIHtcbiAgICAgICAgLy8g56e76Zmk5LqL5Lu255uR5ZCs5ZmoXG4gICAgICAgIGlmICh0aGlzLnNjcm9sbFZpZXcpIHtcbiAgICAgICAgICAgIHRoaXMuc2Nyb2xsVmlldy5ub2RlLm9mZignc2Nyb2xsaW5nJywgdGhpcy5vblNjcm9sbGluZywgdGhpcyk7XG4gICAgICAgICAgICB0aGlzLnNjcm9sbFZpZXcubm9kZS5vZmYoJ3Njcm9sbC1lbmRlZCcsIHRoaXMub25TY3JvbGxFbmRlZCwgdGhpcyk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDliJ3lp4vljJblhbPljaHmlbDmja5cbiAgICAgKi9cbiAgICBwcml2YXRlIGluaXRMZXZlbERhdGEoKSB7XG4gICAgICAgIHRoaXMubGV2ZWxEYXRhTGlzdCA9IFtdO1xuXG4gICAgICAgIC8vIOWIneWni+WMluWFs+WNoeeKtuaAge+8muesrDHlhbPkuLrlvZPliY3lj6/njqnlhbPljaHvvIzlhbbku5bkuLrplIHlrprnirbmgIFcbiAgICAgICAgZm9yIChsZXQgaSA9IDE7IGkgPD0gdGhpcy50b3RhbExldmVsczsgaSsrKSB7XG4gICAgICAgICAgICBsZXQgc3RhdHVzOiBMZXZlbFN0YXR1cztcbiAgICAgICAgICAgIGlmIChpID09PSAxKSB7XG4gICAgICAgICAgICAgICAgc3RhdHVzID0gTGV2ZWxTdGF0dXMuQ1VSUkVOVDsgLy8g56ysMeWFs+m7mOiupOS4uuW9k+WJjeWPr+eOqeWFs+WNoe+8iOaYvuekuuW8gOWni+a4uOaIj+aMiemSru+8iVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBzdGF0dXMgPSBMZXZlbFN0YXR1cy5MT0NLRUQ7IC8vIOWFtuS7luWFs+WNoemUgeWumlxuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICB0aGlzLmxldmVsRGF0YUxpc3QucHVzaCh7XG4gICAgICAgICAgICAgICAgbGV2ZWxOdW1iZXI6IGksXG4gICAgICAgICAgICAgICAgc3RhdHVzOiBzdGF0dXNcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g6buY6K6k6YCJ5Lit56ys5LiA5YWzXG4gICAgICAgIHRoaXMuY3VycmVudFNlbGVjdGVkTGV2ZWwgPSAxO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWIm+W7uuWFs+WNoemhueebrlxuICAgICAqL1xuICAgIHByaXZhdGUgY3JlYXRlTGV2ZWxJdGVtcygpIHtcbiAgICAgICAgaWYgKCF0aGlzLmNvbnRlbnQpIHtcbiAgICAgICAgICAgIGNjLmVycm9yKFwiQ29udGVudCBub2RlIGlzIG5vdCBhc3NpZ25lZCFcIik7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICAvLyDmuIXnqbrnjrDmnInlhoXlrrlcbiAgICAgICAgdGhpcy5jb250ZW50LnJlbW92ZUFsbENoaWxkcmVuKCk7XG4gICAgICAgIHRoaXMubGV2ZWxOb2RlcyA9IFtdO1xuXG4gICAgICAgIC8vIOiuoeeul+aAu+WuveW6plxuICAgICAgICBjb25zdCB0b3RhbFdpZHRoID0gKHRoaXMudG90YWxMZXZlbHMgLSAxKSAqIHRoaXMubGV2ZWxJdGVtV2lkdGggKyB0aGlzLnNjcmVlbldpZHRoO1xuICAgICAgICB0aGlzLmNvbnRlbnQud2lkdGggPSB0b3RhbFdpZHRoO1xuXG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy50b3RhbExldmVsczsgaSsrKSB7XG4gICAgICAgICAgICBjb25zdCBsZXZlbERhdGEgPSB0aGlzLmxldmVsRGF0YUxpc3RbaV07XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIOWIm+W7uuWFs+WNoeiKgueCuVxuICAgICAgICAgICAgY29uc3QgbGV2ZWxOb2RlID0gdGhpcy5jcmVhdGVMZXZlbE5vZGUobGV2ZWxEYXRhKTtcbiAgICAgICAgICAgIHRoaXMuY29udGVudC5hZGRDaGlsZChsZXZlbE5vZGUpO1xuICAgICAgICAgICAgdGhpcy5sZXZlbE5vZGVzLnB1c2gobGV2ZWxOb2RlKTtcblxuICAgICAgICAgICAgLy8g6K6+572u5L2N572uXG4gICAgICAgICAgICBjb25zdCBwb3NYID0gaSAqIHRoaXMubGV2ZWxJdGVtV2lkdGggLSB0b3RhbFdpZHRoIC8gMiArIHRoaXMuc2NyZWVuV2lkdGggLyAyO1xuICAgICAgICAgICAgbGV2ZWxOb2RlLnNldFBvc2l0aW9uKHBvc1gsIDApO1xuXG4gICAgICAgICAgICAvLyDliJvlu7rov57mjqXnur/vvIjpmaTkuobmnIDlkI7kuIDkuKrlhbPljaHvvIlcbiAgICAgICAgICAgIGlmIChpIDwgdGhpcy50b3RhbExldmVscyAtIDEpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmNyZWF0ZUNvbm5lY3Rpb25MaW5lcyhpLCBwb3NYKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWIm+W7uuWFs+WNoeiKgueCuVxuICAgICAqL1xuICAgIHByaXZhdGUgY3JlYXRlTGV2ZWxOb2RlKGxldmVsRGF0YTogTGV2ZWxEYXRhKTogY2MuTm9kZSB7XG4gICAgICAgIGNvbnN0IG5vZGUgPSBuZXcgY2MuTm9kZShgTGV2ZWxfJHtsZXZlbERhdGEubGV2ZWxOdW1iZXJ9YCk7XG4gICAgICAgIFxuICAgICAgICAvLyDmt7vliqBTcHJpdGXnu4Tku7ZcbiAgICAgICAgY29uc3Qgc3ByaXRlID0gbm9kZS5hZGRDb21wb25lbnQoY2MuU3ByaXRlKTtcbiAgICAgICAgXG4gICAgICAgIC8vIOa3u+WKoExhYmVs57uE5Lu25pi+56S65YWz5Y2h5pWw5a2XXG4gICAgICAgIGNvbnN0IGxhYmVsTm9kZSA9IG5ldyBjYy5Ob2RlKFwiTGV2ZWxMYWJlbFwiKTtcbiAgICAgICAgY29uc3QgbGFiZWwgPSBsYWJlbE5vZGUuYWRkQ29tcG9uZW50KGNjLkxhYmVsKTtcbiAgICAgICAgbGFiZWwuc3RyaW5nID0gbGV2ZWxEYXRhLmxldmVsTnVtYmVyLnRvU3RyaW5nKCk7XG5cbiAgICAgICAgLy8g6K6+572u5a2X5L2T5qC35byPXG4gICAgICAgIGxhYmVsLmZvbnRTaXplID0gMzA7XG4gICAgICAgIGxhYmVsLm5vZGUuY29sb3IgPSBjYy5jb2xvcigyNTUsIDI1NSwgMjU1KTsgLy8gI0ZGRkZGRlxuICAgICAgICBsYWJlbC5ob3Jpem9udGFsQWxpZ24gPSBjYy5MYWJlbC5Ib3Jpem9udGFsQWxpZ24uQ0VOVEVSO1xuICAgICAgICBsYWJlbC52ZXJ0aWNhbEFsaWduID0gY2MuTGFiZWwuVmVydGljYWxBbGlnbi5DRU5URVI7XG5cbiAgICAgICAgLy8g5re75Yqg5aSW6L655qGGXG4gICAgICAgIGNvbnN0IG91dGxpbmUgPSBsYWJlbC5hZGRDb21wb25lbnQoY2MuTGFiZWxPdXRsaW5lKTtcbiAgICAgICAgdGhpcy51cGRhdGVMYWJlbE91dGxpbmUob3V0bGluZSwgbGV2ZWxEYXRhLnN0YXR1cyk7XG5cbiAgICAgICAgbGFiZWxOb2RlLnBhcmVudCA9IG5vZGU7XG4gICAgICAgIGxhYmVsTm9kZS5zZXRQb3NpdGlvbigwLCAwKTsgLy8g5bGF5Lit5a+56b2QXG5cbiAgICAgICAgLy8g5re75YqgQnV0dG9u57uE5Lu2XG4gICAgICAgIGNvbnN0IGJ1dHRvbiA9IG5vZGUuYWRkQ29tcG9uZW50KGNjLkJ1dHRvbik7XG4gICAgICAgIGJ1dHRvbi50YXJnZXQgPSBub2RlO1xuXG4gICAgICAgIC8vIOiuvue9rueCueWHu+S6i+S7tlxuICAgICAgICBjb25zdCBldmVudEhhbmRsZXIgPSBuZXcgY2MuQ29tcG9uZW50LkV2ZW50SGFuZGxlcigpO1xuICAgICAgICBldmVudEhhbmRsZXIudGFyZ2V0ID0gdGhpcy5ub2RlO1xuICAgICAgICBldmVudEhhbmRsZXIuY29tcG9uZW50ID0gXCJMZXZlbFNlbGVjdENvbnRyb2xsZXJcIjtcbiAgICAgICAgZXZlbnRIYW5kbGVyLmhhbmRsZXIgPSBcIm9uTGV2ZWxDbGlja2VkXCI7XG4gICAgICAgIGV2ZW50SGFuZGxlci5jdXN0b21FdmVudERhdGEgPSBsZXZlbERhdGEubGV2ZWxOdW1iZXIudG9TdHJpbmcoKTtcbiAgICAgICAgYnV0dG9uLmNsaWNrRXZlbnRzLnB1c2goZXZlbnRIYW5kbGVyKTtcblxuICAgICAgICAvLyDmm7TmlrDlhbPljaHlpJbop4JcbiAgICAgICAgdGhpcy51cGRhdGVMZXZlbE5vZGVBcHBlYXJhbmNlKG5vZGUsIGxldmVsRGF0YSwgZmFsc2UpO1xuXG4gICAgICAgIHJldHVybiBub2RlO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWIm+W7uui/nuaOpee6v+e7hO+8iDnkuKrov57mjqXnur/vvIlcbiAgICAgKi9cbiAgICBwcml2YXRlIGNyZWF0ZUNvbm5lY3Rpb25MaW5lcyhsZXZlbEluZGV4OiBudW1iZXIsIGxldmVsUG9zWDogbnVtYmVyKSB7XG4gICAgICAgIGNvbnN0IHN0YXJ0WCA9IGxldmVsUG9zWCArIDQ2LzIgKyB0aGlzLmxldmVsVG9MaW5lRGlzdGFuY2U7IC8vIOWFs+WNoeWPs+i+uee8mCArIOi3neemu1xuICAgICAgICBjb25zdCB0b3RhbExpbmVXaWR0aCA9ICh0aGlzLmxpbmVDb3VudCAtIDEpICogdGhpcy5saW5lU3BhY2luZztcbiAgICAgICAgY29uc3QgZW5kWCA9IGxldmVsUG9zWCArIHRoaXMubGV2ZWxJdGVtV2lkdGggLSA0Ni8yIC0gdGhpcy5sZXZlbFRvTGluZURpc3RhbmNlOyAvLyDkuIvkuIDkuKrlhbPljaHlt6bovrnnvJggLSDot53nprtcbiAgICAgICAgY29uc3QgYXZhaWxhYmxlV2lkdGggPSBlbmRYIC0gc3RhcnRYO1xuXG4gICAgICAgIC8vIOWmguaenOWPr+eUqOWuveW6puWwj+S6jumcgOimgeeahOWuveW6pu+8jOiwg+aVtOmXtOi3nVxuICAgICAgICBsZXQgYWN0dWFsU3BhY2luZyA9IHRoaXMubGluZVNwYWNpbmc7XG4gICAgICAgIGlmICh0b3RhbExpbmVXaWR0aCA+IGF2YWlsYWJsZVdpZHRoKSB7XG4gICAgICAgICAgICBhY3R1YWxTcGFjaW5nID0gYXZhaWxhYmxlV2lkdGggLyAodGhpcy5saW5lQ291bnQgLSAxKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5saW5lQ291bnQ7IGkrKykge1xuICAgICAgICAgICAgY29uc3QgbGluZU5vZGUgPSB0aGlzLmNyZWF0ZVNpbmdsZUxpbmVOb2RlKCk7XG4gICAgICAgICAgICB0aGlzLmNvbnRlbnQuYWRkQ2hpbGQobGluZU5vZGUpO1xuXG4gICAgICAgICAgICBjb25zdCBsaW5lWCA9IHN0YXJ0WCArIGkgKiBhY3R1YWxTcGFjaW5nO1xuICAgICAgICAgICAgbGluZU5vZGUuc2V0UG9zaXRpb24obGluZVgsIDApO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5Yib5bu65Y2V5Liq6L+e5o6l57q/6IqC54K5XG4gICAgICovXG4gICAgcHJpdmF0ZSBjcmVhdGVTaW5nbGVMaW5lTm9kZSgpOiBjYy5Ob2RlIHtcbiAgICAgICAgY29uc3Qgbm9kZSA9IG5ldyBjYy5Ob2RlKFwiTGluZVwiKTtcbiAgICAgICAgY29uc3Qgc3ByaXRlID0gbm9kZS5hZGRDb21wb25lbnQoY2MuU3ByaXRlKTtcblxuICAgICAgICAvLyDorr7nva7ov57mjqXnur/lpKflsI/kuLo2KjZcbiAgICAgICAgbm9kZS5zZXRDb250ZW50U2l6ZSg2LCA2KTtcblxuICAgICAgICAvLyDliqDovb3ov57mjqXnur/lm77niYdcbiAgICAgICAgY2MucmVzb3VyY2VzLmxvYWQoXCJoYWxsX3BhZ2VfcmVzL0xldmVsX0J0bi9wb3BfbGluZVwiLCBjYy5TcHJpdGVGcmFtZSwgKGVyciwgc3ByaXRlRnJhbWUpID0+IHtcbiAgICAgICAgICAgIGlmICghZXJyICYmIHNwcml0ZUZyYW1lKSB7XG4gICAgICAgICAgICAgICAgc3ByaXRlLnNwcml0ZUZyYW1lID0gc3ByaXRlRnJhbWUgYXMgY2MuU3ByaXRlRnJhbWU7XG4gICAgICAgICAgICAgICAgLy8g5Zu+54mH5Yqg6L295ZCO6YeN5paw6K6+572u5aSn5bCP5Li6Nng277yM56Gu5L+d5LiN6KKr5Zu+54mH5Y6f5aeL5aSn5bCP6KaG55uWXG4gICAgICAgICAgICAgICAgbm9kZS5zZXRDb250ZW50U2l6ZSg2LCA2KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG5cbiAgICAgICAgcmV0dXJuIG5vZGU7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5qOA5p+l5piv5ZCm5Li654m55q6K5YWz5Y2h77yI56ysNeOAgTEw44CBMTXjgIEyMOOAgTI15YWz77yJXG4gICAgICovXG4gICAgcHJpdmF0ZSBpc1NwZWNpYWxMZXZlbChsZXZlbE51bWJlcjogbnVtYmVyKTogYm9vbGVhbiB7XG4gICAgICAgIHJldHVybiBsZXZlbE51bWJlciAlIDUgPT09IDA7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5pu05paw5YWz5Y2h6IqC54K55aSW6KeCXG4gICAgICovXG4gICAgcHJpdmF0ZSB1cGRhdGVMZXZlbE5vZGVBcHBlYXJhbmNlKG5vZGU6IGNjLk5vZGUsIGxldmVsRGF0YTogTGV2ZWxEYXRhLCBpc1NlbGVjdGVkOiBib29sZWFuKSB7XG4gICAgICAgIGNvbnN0IHNwcml0ZSA9IG5vZGUuZ2V0Q29tcG9uZW50KGNjLlNwcml0ZSk7XG4gICAgICAgIGlmICghc3ByaXRlKSByZXR1cm47XG5cbiAgICAgICAgbGV0IGltYWdlUGF0aCA9IFwiXCI7XG4gICAgICAgIGxldCBzaXplID0gY2Muc2l6ZSg0NiwgNDYpO1xuXG4gICAgICAgIC8vIOajgOafpeaYr+WQpuS4uueJueauiuWFs+WNoe+8iOesrDXjgIExMOOAgTE144CBMjDjgIEyNeWFs++8iVxuICAgICAgICBjb25zdCBpc1NwZWNpYWxMZXZlbCA9IHRoaXMuaXNTcGVjaWFsTGV2ZWwobGV2ZWxEYXRhLmxldmVsTnVtYmVyKTtcbiAgICAgICAgY29uc3QgdWlTdWZmaXggPSBpc1NwZWNpYWxMZXZlbCA/IFwiMDFcIiA6IFwiXCI7XG5cblxuICAgICAgICAvLyDmoLnmja7nirbmgIHlkozmmK/lkKbpgInkuK3noa7lrprlm77niYfot6/lvoRcbiAgICAgICAgaWYgKGlzU2VsZWN0ZWQpIHtcbiAgICAgICAgICAgIHNpemUgPSBjYy5zaXplKDg2LCA4Nik7XG4gICAgICAgICAgICBzd2l0Y2ggKGxldmVsRGF0YS5zdGF0dXMpIHtcbiAgICAgICAgICAgICAgICBjYXNlIExldmVsU3RhdHVzLkxPQ0tFRDpcbiAgICAgICAgICAgICAgICAgICAgaW1hZ2VQYXRoID0gYGhhbGxfcGFnZV9yZXMvTGV2ZWxfQnRuL3BvcF9ncmF5JHt1aVN1ZmZpeH1fY2hvb3NlYDtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSBMZXZlbFN0YXR1cy5DVVJSRU5UOlxuICAgICAgICAgICAgICAgICAgICBpbWFnZVBhdGggPSBgaGFsbF9wYWdlX3Jlcy9MZXZlbF9CdG4vcG9wX3llbGxvdyR7dWlTdWZmaXh9X2Nob29zZWA7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgTGV2ZWxTdGF0dXMuQ09NUExFVEVEOlxuICAgICAgICAgICAgICAgICAgICBpbWFnZVBhdGggPSBgaGFsbF9wYWdlX3Jlcy9MZXZlbF9CdG4vcG9wX2dyZWVuJHt1aVN1ZmZpeH1fY2hvb3NlYDtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBzd2l0Y2ggKGxldmVsRGF0YS5zdGF0dXMpIHtcbiAgICAgICAgICAgICAgICBjYXNlIExldmVsU3RhdHVzLkxPQ0tFRDpcbiAgICAgICAgICAgICAgICAgICAgaW1hZ2VQYXRoID0gYGhhbGxfcGFnZV9yZXMvTGV2ZWxfQnRuL3BvcF9ncmF5JHt1aVN1ZmZpeH1gO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIExldmVsU3RhdHVzLkNVUlJFTlQ6XG4gICAgICAgICAgICAgICAgICAgIGltYWdlUGF0aCA9IGBoYWxsX3BhZ2VfcmVzL0xldmVsX0J0bi9wb3BfeWVsbG93JHt1aVN1ZmZpeH1gO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIExldmVsU3RhdHVzLkNPTVBMRVRFRDpcbiAgICAgICAgICAgICAgICAgICAgaW1hZ2VQYXRoID0gYGhhbGxfcGFnZV9yZXMvTGV2ZWxfQnRuL3BvcF9ncmVlbiR7dWlTdWZmaXh9YDtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyDorr7nva7oioLngrnlpKflsI9cbiAgICAgICAgbm9kZS5zZXRDb250ZW50U2l6ZShzaXplKTtcblxuICAgICAgICAvLyDliqDovb3lubborr7nva7lm77niYdcbiAgICAgICAgY2MucmVzb3VyY2VzLmxvYWQoaW1hZ2VQYXRoLCBjYy5TcHJpdGVGcmFtZSwgKGVyciwgc3ByaXRlRnJhbWUpID0+IHtcbiAgICAgICAgICAgIGlmICghZXJyICYmIHNwcml0ZUZyYW1lKSB7XG4gICAgICAgICAgICAgICAgc3ByaXRlLnNwcml0ZUZyYW1lID0gc3ByaXRlRnJhbWUgYXMgY2MuU3ByaXRlRnJhbWU7XG4gICAgICAgICAgICAgICAgLy8g5Zu+54mH5Yqg6L295ZCO5by65Yi26K6+572u5q2j56Gu55qE5aSn5bCP77yM6KaG55uW5Zu+54mH5Y6f5aeL5aSn5bCPXG4gICAgICAgICAgICAgICAgbm9kZS5zZXRDb250ZW50U2l6ZShzaXplKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8g5pu05paw5qCH562+5aSW6L655qGGXG4gICAgICAgIGNvbnN0IGxhYmVsTm9kZSA9IG5vZGUuZ2V0Q2hpbGRCeU5hbWUoXCJMZXZlbExhYmVsXCIpO1xuICAgICAgICBpZiAobGFiZWxOb2RlKSB7XG4gICAgICAgICAgICBjb25zdCBsYWJlbCA9IGxhYmVsTm9kZS5nZXRDb21wb25lbnQoY2MuTGFiZWwpO1xuICAgICAgICAgICAgaWYgKGxhYmVsKSB7XG4gICAgICAgICAgICAgICAgY29uc3Qgb3V0bGluZSA9IGxhYmVsLmdldENvbXBvbmVudChjYy5MYWJlbE91dGxpbmUpO1xuICAgICAgICAgICAgICAgIGlmIChvdXRsaW5lKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMudXBkYXRlTGFiZWxPdXRsaW5lKG91dGxpbmUsIGxldmVsRGF0YS5zdGF0dXMpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOabtOaWsOWFs+WNoeaYvuekulxuICAgICAqL1xuICAgIHByaXZhdGUgdXBkYXRlTGV2ZWxEaXNwbGF5KCkge1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMubGV2ZWxOb2Rlcy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgY29uc3Qgbm9kZSA9IHRoaXMubGV2ZWxOb2Rlc1tpXTtcbiAgICAgICAgICAgIGNvbnN0IGxldmVsRGF0YSA9IHRoaXMubGV2ZWxEYXRhTGlzdFtpXTtcbiAgICAgICAgICAgIGNvbnN0IGlzU2VsZWN0ZWQgPSAobGV2ZWxEYXRhLmxldmVsTnVtYmVyID09PSB0aGlzLmN1cnJlbnRTZWxlY3RlZExldmVsKTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgdGhpcy51cGRhdGVMZXZlbE5vZGVBcHBlYXJhbmNlKG5vZGUsIGxldmVsRGF0YSwgaXNTZWxlY3RlZCk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmu5rliqjliLDmjIflrprlhbPljaFcbiAgICAgKi9cbiAgICBwcml2YXRlIHNjcm9sbFRvTGV2ZWwobGV2ZWxOdW1iZXI6IG51bWJlcikge1xuICAgICAgICBpZiAobGV2ZWxOdW1iZXIgPCAxIHx8IGxldmVsTnVtYmVyID4gdGhpcy50b3RhbExldmVscykgcmV0dXJuO1xuXG4gICAgICAgIGNvbnN0IHRhcmdldEluZGV4ID0gbGV2ZWxOdW1iZXIgLSAxO1xuICAgICAgICBjb25zdCBjb250ZW50V2lkdGggPSB0aGlzLmNvbnRlbnQud2lkdGg7XG4gICAgICAgIGNvbnN0IHNjcm9sbFZpZXdXaWR0aCA9IHRoaXMuc2Nyb2xsVmlldy5ub2RlLndpZHRoO1xuICAgICAgICBcbiAgICAgICAgLy8g6K6h566X55uu5qCH5L2N572u55qE5YGP56e76YePXG4gICAgICAgIGNvbnN0IHRhcmdldE9mZnNldCA9ICh0YXJnZXRJbmRleCAqIHRoaXMubGV2ZWxJdGVtV2lkdGgpIC8gKGNvbnRlbnRXaWR0aCAtIHNjcm9sbFZpZXdXaWR0aCk7XG4gICAgICAgIFxuICAgICAgICAvLyDpmZDliLblgY/np7vph4/lnKjmnInmlYjojIPlm7TlhoVcbiAgICAgICAgY29uc3QgY2xhbXBlZE9mZnNldCA9IGNjLm1pc2MuY2xhbXBmKHRhcmdldE9mZnNldCwgMCwgMSk7XG4gICAgICAgIFxuICAgICAgICAvLyDorr7nva7mu5rliqjkvY3nva5cbiAgICAgICAgdGhpcy5zY3JvbGxWaWV3LnNjcm9sbFRvUGVyY2VudEhvcml6b250YWwoY2xhbXBlZE9mZnNldCwgMC4zKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDlhbPljaHngrnlh7vkuovku7blpITnkIZcbiAgICAgKi9cbiAgICBwdWJsaWMgb25MZXZlbENsaWNrZWQoZXZlbnQ6IGNjLkV2ZW50LCBjdXN0b21FdmVudERhdGE6IHN0cmluZykge1xuICAgICAgICBjb25zdCBsZXZlbE51bWJlciA9IHBhcnNlSW50KGN1c3RvbUV2ZW50RGF0YSk7XG5cbiAgICAgICAgLy8g5YWB6K646YCJ5oup5Lu75L2V5YWz5Y2h77yI5YyF5ous5pyq6Kej6ZSB55qE77yJXG4gICAgICAgIC8vIOabtOaWsOW9k+WJjemAieS4reWFs+WNoVxuICAgICAgICB0aGlzLmN1cnJlbnRTZWxlY3RlZExldmVsID0gbGV2ZWxOdW1iZXI7XG4gICAgICAgIHRoaXMudXBkYXRlTGV2ZWxEaXNwbGF5KCk7XG5cbiAgICAgICAgLy8g5rua5Yqo5Yiw6YCJ5Lit5YWz5Y2hXG4gICAgICAgIHRoaXMuaXNBdXRvU2Nyb2xsaW5nID0gdHJ1ZTtcbiAgICAgICAgdGhpcy5zY3JvbGxUb0xldmVsKGxldmVsTnVtYmVyKTtcblxuICAgICAgXG5cbiAgICAgICAgLy8g6YCa55+l5YWz5Y2h6YCJ5oup5Y+Y5YyWXG4gICAgICAgIGlmICh0aGlzLm9uTGV2ZWxTZWxlY3Rpb25DaGFuZ2VkKSB7XG4gICAgICAgICAgICB0aGlzLm9uTGV2ZWxTZWxlY3Rpb25DaGFuZ2VkKGxldmVsTnVtYmVyKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOi/memHjOWPr+S7pea3u+WKoOi/m+WFpeWFs+WNoeeahOmAu+i+kVxuICAgICAgICAvLyB0aGlzLmVudGVyTGV2ZWwobGV2ZWxOdW1iZXIpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOiuvue9ruWFs+WNoeeKtuaAgVxuICAgICAqL1xuICAgIHB1YmxpYyBzZXRMZXZlbFN0YXR1cyhsZXZlbE51bWJlcjogbnVtYmVyLCBzdGF0dXM6IExldmVsU3RhdHVzKSB7XG4gICAgICAgIGlmIChsZXZlbE51bWJlciA8IDEgfHwgbGV2ZWxOdW1iZXIgPiB0aGlzLnRvdGFsTGV2ZWxzKSByZXR1cm47XG5cbiAgICAgICAgdGhpcy5sZXZlbERhdGFMaXN0W2xldmVsTnVtYmVyIC0gMV0uc3RhdHVzID0gc3RhdHVzO1xuICAgICAgICB0aGlzLnVwZGF0ZUxldmVsRGlzcGxheSgpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOiOt+WPluW9k+WJjemAieS4reeahOWFs+WNoVxuICAgICAqL1xuICAgIHB1YmxpYyBnZXRDdXJyZW50U2VsZWN0ZWRMZXZlbCgpOiBudW1iZXIge1xuICAgICAgICByZXR1cm4gdGhpcy5jdXJyZW50U2VsZWN0ZWRMZXZlbDtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDojrflj5bmjIflrprlhbPljaHnmoTmlbDmja5cbiAgICAgKi9cbiAgICBwdWJsaWMgZ2V0TGV2ZWxEYXRhKGxldmVsTnVtYmVyOiBudW1iZXIpOiBMZXZlbERhdGEgfCBudWxsIHtcbiAgICAgICAgaWYgKGxldmVsTnVtYmVyIDwgMSB8fCBsZXZlbE51bWJlciA+IHRoaXMudG90YWxMZXZlbHMpIHJldHVybiBudWxsO1xuICAgICAgICByZXR1cm4gdGhpcy5sZXZlbERhdGFMaXN0W2xldmVsTnVtYmVyIC0gMV07XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6K6+572u5YWz5Y2h6L+b5bqm77yI5LuO5aSW6YOo6LCD55So77yM5q+U5aaC5LuO5ZCO56uv6I635Y+W5pWw5o2u5ZCO77yJXG4gICAgICogQHBhcmFtIGxldmVsUHJvZ3Jlc3NEYXRhIOWFs+WNoei/m+W6puaVsOaNru+8jOWMheWQq2NsZWFyZWRMZXZlbHMsIGN1cnJlbnRMZXZlbCwgdG90YWxMZXZlbHNcbiAgICAgKi9cbiAgICBwdWJsaWMgc2V0TGV2ZWxQcm9ncmVzcyhsZXZlbFByb2dyZXNzRGF0YTogYW55KSB7XG4gICAgICAgIGlmICghbGV2ZWxQcm9ncmVzc0RhdGEpIHJldHVybjtcblxuICAgICAgICBjb25zdCB7IGNsZWFyZWRMZXZlbHMsIGN1cnJlbnRMZXZlbCwgdG90YWxMZXZlbHMgfSA9IGxldmVsUHJvZ3Jlc3NEYXRhO1xuXG4gICAgICAgIC8vIOmqjOivgeaVsOaNruacieaViOaAp1xuICAgICAgICBpZiAoY2xlYXJlZExldmVscyA8IDAgfHwgY3VycmVudExldmVsIDwgMSB8fCBjdXJyZW50TGV2ZWwgPiB0b3RhbExldmVscykgcmV0dXJuO1xuXG4gICAgICAgIC8vIOabtOaWsOaAu+WFs+WNoeaVsO+8iOWmguaenOWQjuerr+S8oOS6hueahOivne+8iVxuICAgICAgICBpZiAodG90YWxMZXZlbHMgJiYgdG90YWxMZXZlbHMgPiAwKSB7XG4gICAgICAgICAgICB0aGlzLnRvdGFsTGV2ZWxzID0gdG90YWxMZXZlbHM7XG4gICAgICAgIH1cblxuICAgICAgICAvLyDph43mlrDorr7nva7miYDmnInlhbPljaHnirbmgIFcbiAgICAgICAgZm9yIChsZXQgaSA9IDE7IGkgPD0gdGhpcy50b3RhbExldmVsczsgaSsrKSB7XG4gICAgICAgICAgICBsZXQgc3RhdHVzOiBMZXZlbFN0YXR1cztcbiAgICAgICAgICAgIGlmIChpIDwgY3VycmVudExldmVsKSB7XG4gICAgICAgICAgICAgICAgc3RhdHVzID0gTGV2ZWxTdGF0dXMuQ09NUExFVEVEOyAvLyDlvZPliY3lhbPljaHkuYvliY3nmoTpg73mmK/lt7LlrozmiJDvvIjnu7/oibLvvIlcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoaSA9PT0gY3VycmVudExldmVsKSB7XG4gICAgICAgICAgICAgICAgc3RhdHVzID0gTGV2ZWxTdGF0dXMuQ1VSUkVOVDsgLy8g5b2T5YmN5YWz5Y2h77yI6buE6Imy6YCJ5oupVUnvvIlcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgc3RhdHVzID0gTGV2ZWxTdGF0dXMuTE9DS0VEOyAvLyDlvZPliY3lhbPljaHkuYvlkI7nmoTpg73mmK/mnKrop6PplIHvvIjngbDoibLvvIlcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgdGhpcy5sZXZlbERhdGFMaXN0W2kgLSAxXS5zdGF0dXMgPSBzdGF0dXM7XG4gICAgICAgIH1cblxuICAgICAgICAvLyDmm7TmlrDlvZPliY3pgInkuK3lhbPljaHkuLrlkI7nq6/mjIflrprnmoRjdXJyZW50TGV2ZWxcbiAgICAgICAgdGhpcy5jdXJyZW50U2VsZWN0ZWRMZXZlbCA9IGN1cnJlbnRMZXZlbDtcbiAgICAgICAgdGhpcy51cGRhdGVMZXZlbERpc3BsYXkoKTtcbiAgICAgICAgdGhpcy5zY3JvbGxUb0xldmVsKGN1cnJlbnRMZXZlbCk7XG5cbiAgICAgICAgLy8g6YCa55+l5YWz5Y2h6YCJ5oup5Y+Y5YyWXG4gICAgICAgIGlmICh0aGlzLm9uTGV2ZWxTZWxlY3Rpb25DaGFuZ2VkKSB7XG4gICAgICAgICAgICB0aGlzLm9uTGV2ZWxTZWxlY3Rpb25DaGFuZ2VkKGN1cnJlbnRMZXZlbCk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDorr7nva7lhbPljaHov5vluqbvvIjlhbzlrrnml6fmjqXlj6PvvIlcbiAgICAgKiBAcGFyYW0gY29tcGxldGVkTGV2ZWxzIOW3suWujOaIkOeahOWFs+WNoeaVsFxuICAgICAqL1xuICAgIHB1YmxpYyBzZXRMZXZlbFByb2dyZXNzTGVnYWN5KGNvbXBsZXRlZExldmVsczogbnVtYmVyKSB7XG4gICAgICAgIC8vIOi9rOaNouS4uuaWsOeahOaVsOaNruagvOW8j1xuICAgICAgICBjb25zdCBsZXZlbFByb2dyZXNzRGF0YSA9IHtcbiAgICAgICAgICAgIGNsZWFyZWRMZXZlbHM6IGNvbXBsZXRlZExldmVscyxcbiAgICAgICAgICAgIGN1cnJlbnRMZXZlbDogTWF0aC5taW4oY29tcGxldGVkTGV2ZWxzICsgMSwgdGhpcy50b3RhbExldmVscyksXG4gICAgICAgICAgICB0b3RhbExldmVsczogdGhpcy50b3RhbExldmVsc1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLnNldExldmVsUHJvZ3Jlc3MobGV2ZWxQcm9ncmVzc0RhdGEpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOino+mUgeS4i+S4gOWFs1xuICAgICAqL1xuICAgIHB1YmxpYyB1bmxvY2tOZXh0TGV2ZWwoKSB7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5sZXZlbERhdGFMaXN0Lmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5sZXZlbERhdGFMaXN0W2ldLnN0YXR1cyA9PT0gTGV2ZWxTdGF0dXMuTE9DS0VEKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5sZXZlbERhdGFMaXN0W2ldLnN0YXR1cyA9IExldmVsU3RhdHVzLkNVUlJFTlQ7XG4gICAgICAgICAgICAgICAgdGhpcy51cGRhdGVMZXZlbERpc3BsYXkoKTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWujOaIkOW9k+WJjeWFs+WNoVxuICAgICAqL1xuICAgIHB1YmxpYyBjb21wbGV0ZUN1cnJlbnRMZXZlbCgpIHtcbiAgICAgICAgY29uc3QgY3VycmVudExldmVsRGF0YSA9IHRoaXMubGV2ZWxEYXRhTGlzdFt0aGlzLmN1cnJlbnRTZWxlY3RlZExldmVsIC0gMV07XG4gICAgICAgIGlmIChjdXJyZW50TGV2ZWxEYXRhLnN0YXR1cyA9PT0gTGV2ZWxTdGF0dXMuQ1VSUkVOVCkge1xuICAgICAgICAgICAgY3VycmVudExldmVsRGF0YS5zdGF0dXMgPSBMZXZlbFN0YXR1cy5DT01QTEVURUQ7XG4gICAgICAgICAgICB0aGlzLnVubG9ja05leHRMZXZlbCgpO1xuICAgICAgICAgICAgdGhpcy51cGRhdGVMZXZlbERpc3BsYXkoKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOiuvue9rua7keWKqOS6i+S7tuebkeWQrFxuICAgICAqL1xuICAgIHByaXZhdGUgc2V0dXBTY3JvbGxFdmVudHMoKSB7XG4gICAgICAgIGlmICghdGhpcy5zY3JvbGxWaWV3KSByZXR1cm47XG5cbiAgICAgICAgLy8g55uR5ZCs5ruR5Yqo5Lit5LqL5Lu2XG4gICAgICAgIHRoaXMuc2Nyb2xsVmlldy5ub2RlLm9uKCdzY3JvbGxpbmcnLCB0aGlzLm9uU2Nyb2xsaW5nLCB0aGlzKTtcblxuICAgICAgICAvLyDnm5HlkKzmu5Hliqjnu5PmnZ/kuovku7ZcbiAgICAgICAgdGhpcy5zY3JvbGxWaWV3Lm5vZGUub24oJ3Njcm9sbC1lbmRlZCcsIHRoaXMub25TY3JvbGxFbmRlZCwgdGhpcyk7XG5cbiAgICAgXG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5ruR5Yqo5Lit5LqL5Lu25aSE55CGXG4gICAgICovXG4gICAgcHJpdmF0ZSBvblNjcm9sbGluZygpIHtcbiAgICAgICAgdGhpcy51cGRhdGVTZWxlY3RlZExldmVsQnlQb3NpdGlvbigpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOa7keWKqOe7k+adn+S6i+S7tuWkhOeQhlxuICAgICAqL1xuICAgIHByaXZhdGUgb25TY3JvbGxFbmRlZCgpIHtcbiAgICAgICAgLy8g5aaC5p6c5piv6Ieq5Yqo5rua5Yqo6Kem5Y+R55qE5LqL5Lu277yM5b+955WlXG4gICAgICAgIGlmICh0aGlzLmlzQXV0b1Njcm9sbGluZykge1xuICAgICAgICAgICAgdGhpcy5pc0F1dG9TY3JvbGxpbmcgPSBmYWxzZTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIHRoaXMudXBkYXRlU2VsZWN0ZWRMZXZlbEJ5UG9zaXRpb24oKTtcbiAgICAgICAgLy8g5ruR5Yqo57uT5p2f5ZCO77yM5bCG6YCJ5Lit55qE5YWz5Y2h5Zu65a6a5bGF5LitXG4gICAgICAgIHRoaXMuaXNBdXRvU2Nyb2xsaW5nID0gdHJ1ZTtcbiAgICAgICAgdGhpcy5zY3JvbGxUb0xldmVsKHRoaXMuY3VycmVudFNlbGVjdGVkTGV2ZWwpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOagueaNruW9k+WJjeS9jee9ruabtOaWsOmAieS4reeahOWFs+WNoVxuICAgICAqL1xuICAgIHByaXZhdGUgdXBkYXRlU2VsZWN0ZWRMZXZlbEJ5UG9zaXRpb24oKSB7XG4gICAgICAgIGlmICghdGhpcy5zY3JvbGxWaWV3IHx8ICF0aGlzLmNvbnRlbnQpIHJldHVybjtcblxuICAgICAgICAvLyDojrflj5ZTY3JvbGxWaWV355qE5Lit5b+D5L2N572u77yI55u45a+55LqOU2Nyb2xsVmlld+iKgueCue+8iVxuICAgICAgICBjb25zdCBzY3JvbGxWaWV3Q2VudGVyWCA9IDA7IC8vIFNjcm9sbFZpZXfnmoTkuK3lv4PlsLHmmK94PTBcblxuICAgICAgICAvLyDmib7liLDmnIDmjqXov5FTY3JvbGxWaWV35Lit5b+D5L2N572u55qE5YWz5Y2hXG4gICAgICAgIGxldCBjbG9zZXN0TGV2ZWwgPSAxO1xuICAgICAgICBsZXQgbWluRGlzdGFuY2UgPSBOdW1iZXIuTUFYX1ZBTFVFO1xuXG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5sZXZlbE5vZGVzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICBjb25zdCBsZXZlbE5vZGUgPSB0aGlzLmxldmVsTm9kZXNbaV07XG5cbiAgICAgICAgICAgIC8vIOWwhuWFs+WNoeiKgueCueeahOacrOWcsOWdkOagh+i9rOaNouS4ulNjcm9sbFZpZXflnZDmoIfns7tcbiAgICAgICAgICAgIGNvbnN0IGxldmVsV29ybGRQb3MgPSB0aGlzLmNvbnRlbnQuY29udmVydFRvV29ybGRTcGFjZUFSKGxldmVsTm9kZS5wb3NpdGlvbik7XG4gICAgICAgICAgICBjb25zdCBsZXZlbFNjcm9sbFZpZXdQb3MgPSB0aGlzLnNjcm9sbFZpZXcubm9kZS5jb252ZXJ0VG9Ob2RlU3BhY2VBUihsZXZlbFdvcmxkUG9zKTtcblxuICAgICAgICAgICAgLy8g6K6h566X5YWz5Y2h5LiOU2Nyb2xsVmlld+S4reW/g+eahOi3neemu1xuICAgICAgICAgICAgY29uc3QgZGlzdGFuY2UgPSBNYXRoLmFicyhsZXZlbFNjcm9sbFZpZXdQb3MueCAtIHNjcm9sbFZpZXdDZW50ZXJYKTtcblxuICAgICAgICAgICAgaWYgKGRpc3RhbmNlIDwgbWluRGlzdGFuY2UpIHtcbiAgICAgICAgICAgICAgICBtaW5EaXN0YW5jZSA9IGRpc3RhbmNlO1xuICAgICAgICAgICAgICAgIGNsb3Nlc3RMZXZlbCA9IGkgKyAxO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8g5aaC5p6c6YCJ5Lit55qE5YWz5Y2h5Y+R55Sf5Y+Y5YyW77yM5pu05paw5pi+56S6XG4gICAgICAgIGlmIChjbG9zZXN0TGV2ZWwgIT09IHRoaXMuY3VycmVudFNlbGVjdGVkTGV2ZWwpIHtcbiAgICAgICAgICAgIHRoaXMuY3VycmVudFNlbGVjdGVkTGV2ZWwgPSBjbG9zZXN0TGV2ZWw7XG4gICAgICAgICAgICB0aGlzLnVwZGF0ZUxldmVsRGlzcGxheSgpO1xuICAgICAgICAgICBcblxuICAgICAgICAgICAgLy8g6YCa55+l5YWz5Y2h6YCJ5oup5Y+Y5YyWXG4gICAgICAgICAgICBpZiAodGhpcy5vbkxldmVsU2VsZWN0aW9uQ2hhbmdlZCkge1xuICAgICAgICAgICAgICAgIHRoaXMub25MZXZlbFNlbGVjdGlvbkNoYW5nZWQoY2xvc2VzdExldmVsKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOS/ruWkjVNjcm9sbFZpZXfnmoRTY3JvbGxiYXLpl67pophcbiAgICAgKi9cbiAgICBwcml2YXRlIGZpeFNjcm9sbFZpZXdTY3JvbGxiYXIoKSB7XG4gICAgICAgIGlmICh0aGlzLnNjcm9sbFZpZXcgJiYgdGhpcy5jb250ZW50KSB7XG4gICAgICAgICAgICBTY3JvbGxWaWV3SGVscGVyLnNldHVwSG9yaXpvbnRhbFNjcm9sbFZpZXcodGhpcy5zY3JvbGxWaWV3LCB0aGlzLmNvbnRlbnQpO1xuICAgICAgICAgICAgU2Nyb2xsVmlld0hlbHBlci5kZWJ1Z1Njcm9sbFZpZXcodGhpcy5zY3JvbGxWaWV3LCBcIkxldmVsU2VsZWN0Q29udHJvbGxlclwiKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOiwg+ivleWPguaVsOS/oeaBr1xuICAgICAqL1xuICAgIHB1YmxpYyBkZWJ1Z1BhcmFtZXRlcnMoKSB7XG4gICAgICAgXG5cbiAgICAgICAgLy8g6K6h566X6L+e5o6l57q/5oC75a695bqmXG4gICAgICAgIGNvbnN0IHRvdGFsTGluZVdpZHRoID0gKHRoaXMubGluZUNvdW50IC0gMSkgKiB0aGlzLmxpbmVTcGFjaW5nO1xuICAgICAgXG5cbiAgICAgICAgLy8g6K6h566X5Y+v55So5a695bqmXG4gICAgICAgIGNvbnN0IGF2YWlsYWJsZVdpZHRoID0gdGhpcy5sZXZlbEl0ZW1XaWR0aCAtIDQ2IC0gKHRoaXMubGV2ZWxUb0xpbmVEaXN0YW5jZSAqIDIpO1xuICAgICAgXG5cbiAgICAgICAgaWYgKHRvdGFsTGluZVdpZHRoID4gYXZhaWxhYmxlV2lkdGgpIHtcbiAgICAgICAgICAgIGNvbnN0IGFkanVzdGVkU3BhY2luZyA9IGF2YWlsYWJsZVdpZHRoIC8gKHRoaXMubGluZUNvdW50IC0gMSk7XG4gICAgICAgICAgIFxuICAgICAgICB9IGVsc2Uge1xuICAgXG4gICAgICAgIH1cblxuICAgICAgXG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5pu05paw5qCH562+5aSW6L655qGGXG4gICAgICovXG4gICAgcHJpdmF0ZSB1cGRhdGVMYWJlbE91dGxpbmUob3V0bGluZTogY2MuTGFiZWxPdXRsaW5lLCBzdGF0dXM6IExldmVsU3RhdHVzKSB7XG4gICAgICAgIGxldCBvdXRsaW5lQ29sb3I6IGNjLkNvbG9yO1xuICAgICAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgICAgICAgY2FzZSBMZXZlbFN0YXR1cy5MT0NLRUQ6XG4gICAgICAgICAgICAgICAgLy8g5pyq6Kej6ZSB6L655qGG5Li6ICM3QjdCN0JcbiAgICAgICAgICAgICAgICBvdXRsaW5lQ29sb3IgPSBjYy5jb2xvcigxMjMsIDEyMywgMTIzKTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgTGV2ZWxTdGF0dXMuQ1VSUkVOVDpcbiAgICAgICAgICAgICAgICAvLyDlvZPliY3njqnliLDnmoTlhbPljaHovrnmoYYgI0NGNTgwMFxuICAgICAgICAgICAgICAgIG91dGxpbmVDb2xvciA9IGNjLmNvbG9yKDIwNywgODgsIDApO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSBMZXZlbFN0YXR1cy5DT01QTEVURUQ6XG4gICAgICAgICAgICAgICAgLy8g5bey6Kej6ZSB6L655qGG5Li6ICMxMTlDMEZcbiAgICAgICAgICAgICAgICBvdXRsaW5lQ29sb3IgPSBjYy5jb2xvcigxNywgMTU2LCAxNSk7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgIG91dGxpbmVDb2xvciA9IGNjLmNvbG9yKDEyMywgMTIzLCAxMjMpO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG5cbiAgICAgICAgb3V0bGluZS5jb2xvciA9IG91dGxpbmVDb2xvcjtcbiAgICAgICAgb3V0bGluZS53aWR0aCA9IDE7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6YeN5paw5Yib5bu65YWz5Y2h6aG555uu77yI55So5LqO5Y+C5pWw6LCD5pW05ZCO5Yi35paw77yJXG4gICAgICovXG4gICAgcHVibGljIHJlZnJlc2hMZXZlbEl0ZW1zKCkge1xuICAgICAgICB0aGlzLmNyZWF0ZUxldmVsSXRlbXMoKTtcbiAgICAgICAgdGhpcy51cGRhdGVMZXZlbERpc3BsYXkoKTtcbiAgICAgICAgdGhpcy5zY3JvbGxUb0xldmVsKHRoaXMuY3VycmVudFNlbGVjdGVkTGV2ZWwpO1xuICAgIH1cblxuXG59XG4iXX0=