
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/LevelPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c8e13uMxpxGELez69T2SfJx', 'LevelPageController');
// scripts/level/LevelPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var Tools_1 = require("../util/Tools");
var Config_1 = require("../util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelPageController = /** @class */ (function (_super) {
    __extends(LevelPageController, _super);
    function LevelPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 返回按钮
        _this.backButton = null;
        // 开始游戏按钮
        _this.startGameButton = null;
        // 地雷数UI标签
        _this.mineCountLabel = null;
        // 退出游戏弹窗
        _this.leaveDialogController = null;
        // level_page节点
        _this.levelPageNode = null;
        // game_map_1节点
        _this.gameMap1Node = null;
        // game_map_2节点
        _this.gameMap2Node = null;
        // 方形地图节点引用
        _this.qipan8x8Node = null; // level_page/game_map_1/chess_bg/qipan8*8
        _this.qipan8x9Node = null; // level_page/game_map_1/chess_bg/qipan8*9
        _this.qipan9x9Node = null; // level_page/game_map_1/chess_bg/qipan9*9
        _this.qipan9x10Node = null; // level_page/game_map_1/chess_bg/qipan9*10
        _this.qipan10x10Node = null; // level_page/game_map_1/chess_bg/qipan10*10
        // 特殊关卡节点引用
        _this.levelS001Node = null; // level_page/game_map_2/game_bg/Level_S001
        _this.levelS002Node = null; // level_page/game_map_2/game_bg/Level_S002
        _this.levelS003Node = null; // level_page/game_map_2/game_bg/Level_S003
        _this.levelS004Node = null; // level_page/game_map_2/game_bg/Level_S004
        _this.levelS005Node = null; // level_page/game_map_2/game_bg/Level_S005
        _this.levelS006Node = null; // level_page/game_map_2/game_bg/Level_S006
        // 当前关卡数据
        _this.currentLevel = 1;
        _this.currentLevelInfo = null;
        return _this;
    }
    LevelPageController.prototype.onLoad = function () {
        var _this = this;
        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式
        if (this.backButton) {
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
    };
    LevelPageController.prototype.onEnable = function () {
        var _this = this;
        // 当关卡页面被激活时，确保显示正确的关卡
        cc.log("LevelPageController onEnable() - \u5F53\u524D\u5173\u5361: " + this.currentLevel);
        // 延迟一帧执行，确保所有初始化完成
        this.scheduleOnce(function () {
            if (_this.currentLevel > 1) {
                cc.log("\u5173\u5361\u9875\u9762\u6FC0\u6D3B\uFF0C\u663E\u793A\u5173\u5361 " + _this.currentLevel);
                _this.enterLevel(_this.currentLevel);
            }
        }, 0);
    };
    LevelPageController.prototype.start = function () {
        if (this.levelPageNode) {
            cc.log("LevelPageController start() - levelPageNode \u6FC0\u6D3B\u72B6\u6001: " + this.levelPageNode.active);
        }
        // 如果当前关卡还是默认值1，说明还没有被正确设置，先隐藏所有地图
        if (this.currentLevel === 1) {
            cc.log("关卡编号还是默认值1，隐藏所有地图节点等待正确的关卡设置");
            this.hideAllMapNodes();
        }
        else {
            // 如果已经设置了正确的关卡编号，确保显示正确的关卡
            cc.log("\u5173\u5361\u7F16\u53F7\u5DF2\u8BBE\u7F6E\u4E3A " + this.currentLevel + "\uFF0C\u786E\u4FDD\u663E\u793A\u6B63\u786E\u7684\u5173\u5361");
            this.enterLevel(this.currentLevel);
        }
    };
    /**
     * 返回按钮点击事件
     */
    LevelPageController.prototype.onBackButtonClick = function () {
        cc.log("关卡页面返回按钮被点击");
        // 弹出确认退出对话框，type=1表示退出本局游戏
        if (this.leaveDialogController) {
            this.leaveDialogController.show(1, function () {
                cc.log("退出游戏确认对话框已关闭");
            });
        }
        else {
            cc.warn("LeaveDialogController 未配置");
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelPageController.prototype.onStartGameButtonClick = function () {
        cc.log("\u5F00\u59CB\u6E38\u620F - \u5173\u5361 " + this.currentLevel);
        // 发送ExtendLevelInfo消息到后端获取地图数据
        var request = {
            levelNumber: this.currentLevel
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
    };
    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    LevelPageController.prototype.onExtendLevelInfo = function (levelInfo) {
        cc.log("\uD83D\uDCE8 \u6536\u5230\u5173\u5361\u4FE1\u606F\u54CD\u5E94");
        cc.log("   - \u524D\u7AEF\u5F53\u524D\u5173\u5361: " + this.currentLevel);
        cc.log("   - \u540E\u7AEF\u8FD4\u56DElevelId: " + levelInfo.levelId);
        cc.log("   - \u540E\u7AEF\u8FD4\u56DElevelNumber: " + levelInfo.levelNumber);
        cc.log("   - \u540E\u7AEF\u8FD4\u56DEmineCount: " + levelInfo.mineCount);
        this.currentLevelInfo = levelInfo;
        // 更新地雷数UI
        this.updateMineCountUI(levelInfo.mineCount);
        // 使用当前设置的关卡编号，而不是后端返回的levelId
        // 因为后端的levelId可能与前端的关卡编号不一致
        cc.log("\uD83C\uDFAE \u4F7F\u7528\u524D\u7AEF\u5173\u5361\u7F16\u53F7 " + this.currentLevel + " \u8FDB\u5165\u5173\u5361");
        this.enterLevel(this.currentLevel);
    };
    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    LevelPageController.prototype.updateMineCountUI = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
            cc.log("\u66F4\u65B0\u5730\u96F7\u6570UI: " + mineCount);
        }
    };
    /**
     * 根据关卡数进入相应的关卡
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.enterLevel = function (levelNumber) {
        cc.log("\uD83D\uDE80 enterLevel() \u88AB\u8C03\u7528 - \u5173\u5361\u7F16\u53F7: " + levelNumber);
        cc.log("   - this.currentLevel: " + this.currentLevel);
        cc.log("   - \u8282\u70B9\u6FC0\u6D3B\u72B6\u6001: " + this.node.active);
        // 特别关注第30关
        if (levelNumber === 30) {
            cc.log("\uD83C\uDFAF \u8FD9\u662F\u7B2C30\u5173\uFF01\u5E94\u8BE5\u663E\u793A Level_S006");
        }
        // 调试：检查地图节点是否已配置
        cc.log("地图节点配置状态：");
        cc.log("qipan8x8Node: " + (this.qipan8x8Node ? '✅' : '❌'));
        cc.log("qipan8x9Node: " + (this.qipan8x9Node ? '✅' : '❌'));
        cc.log("qipan9x9Node: " + (this.qipan9x9Node ? '✅' : '❌'));
        cc.log("qipan9x10Node: " + (this.qipan9x10Node ? '✅' : '❌'));
        cc.log("qipan10x10Node: " + (this.qipan10x10Node ? '✅' : '❌'));
        cc.log("levelS001Node: " + (this.levelS001Node ? '✅' : '❌'));
        cc.log("levelS006Node: " + (this.levelS006Node ? '✅' : '❌'));
        // 先隐藏所有地图容器
        this.hideAllMapContainers();
        // 根据关卡数显示对应的地图节点
        if (levelNumber >= 1 && levelNumber <= 4) {
            // 第1-4关，打开level_page/game_map_1/chess_bg/qipan8*8
            this.showGameMap1();
            this.showMapNode(this.qipan8x8Node, "qipan8*8");
        }
        else if (levelNumber === 5) {
            // 第5关，打开level_page/game_map_2/game_bg/Level_S001
            this.showGameMap2();
            this.showMapNode(this.levelS001Node, "Level_S001");
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            // 第6-9关，打开level_page/game_map_1/chess_bg/qipan8*9
            this.showGameMap1();
            this.showMapNode(this.qipan8x9Node, "qipan8*9");
        }
        else if (levelNumber === 10) {
            // 第10关，打开level_page/game_map_2/game_bg/Level_S002
            this.showGameMap2();
            this.showMapNode(this.levelS002Node, "Level_S002");
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            // 第11-14关，打开level_page/game_map_1/chess_bg/qipan9*9
            this.showGameMap1();
            this.showMapNode(this.qipan9x9Node, "qipan9*9");
        }
        else if (levelNumber === 15) {
            // 第15关，打开level_page/game_map_2/game_bg/Level_S003
            this.showGameMap2();
            this.showMapNode(this.levelS003Node, "Level_S003");
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            // 第16-19关，打开level_page/game_map_1/chess_bg/qipan9*10
            this.showGameMap1();
            this.showMapNode(this.qipan9x10Node, "qipan9*10");
        }
        else if (levelNumber === 20) {
            // 第20关，打开level_page/game_map_2/game_bg/Level_S004
            this.showGameMap2();
            this.showMapNode(this.levelS004Node, "Level_S004");
        }
        else if (levelNumber >= 21 && levelNumber <= 24) {
            // 第21-24关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showGameMap1();
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        }
        else if (levelNumber === 25) {
            // 第25关，打开level_page/game_map_2/game_bg/Level_S005
            this.showGameMap2();
            this.showMapNode(this.levelS005Node, "Level_S005");
        }
        else if (levelNumber >= 26 && levelNumber <= 29) {
            // 第26-29关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showGameMap1();
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        }
        else if (levelNumber === 30) {
            // 第30关，打开level_page/game_map_2/game_bg/Level_S006
            cc.log("\uD83C\uDFAF \u5904\u7406\u7B2C30\u5173 - \u663E\u793A Level_S006");
            this.showGameMap2();
            this.showMapNode(this.levelS006Node, "Level_S006");
        }
        else {
            cc.warn("\u274C \u672A\u77E5\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
            cc.warn("   - \u8FD9\u53EF\u80FD\u662F\u4E3A\u4EC0\u4E48\u663E\u793A\u7B2C1\u5173\u7684\u539F\u56E0\uFF01");
        }
    };
    /**
     * 显示指定的地图节点
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    LevelPageController.prototype.showMapNode = function (mapNode, mapName) {
        if (mapNode) {
            cc.log("\uD83D\uDD0D \u51C6\u5907\u663E\u793A\u5730\u56FE\u8282\u70B9: " + mapName);
            cc.log("\u8282\u70B9\u8DEF\u5F84: " + mapNode.name);
            cc.log("\u8282\u70B9\u5F53\u524D\u72B6\u6001 - active: " + mapNode.active + ", parent: " + (mapNode.parent ? mapNode.parent.name : 'null'));
            mapNode.active = true;
            // 检查父节点链是否都是激活状态
            var currentNode = mapNode.parent;
            var parentChain = [];
            while (currentNode) {
                // 避免访问场景节点的 active 属性
                if (currentNode.name === 'game_scene' || currentNode.name === 'Scene') {
                    parentChain.push(currentNode.name + "(Scene)");
                }
                else {
                    parentChain.push(currentNode.name + "(" + currentNode.active + ")");
                }
                currentNode = currentNode.parent;
            }
            cc.log("\u7236\u8282\u70B9\u94FE: " + parentChain.join(' -> '));
            cc.log("\u2705 \u663E\u793A\u5730\u56FE\u8282\u70B9: " + mapName + " - \u8BBE\u7F6E\u540E\u72B6\u6001: " + mapNode.active);
        }
        else {
            cc.warn("\u274C \u5730\u56FE\u8282\u70B9\u672A\u627E\u5230: " + mapName);
            cc.warn("\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u4E3A LevelPageController \u914D\u7F6E " + mapName + " \u8282\u70B9\u5C5E\u6027");
        }
    };
    /**
     * 隐藏所有地图节点
     */
    LevelPageController.prototype.hideAllMapNodes = function () {
        var allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];
        allMapNodes.forEach(function (node) {
            if (node) {
                node.active = false;
            }
        });
    };
    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.setCurrentLevel = function (levelNumber) {
        cc.log("\uD83C\uDFAF LevelPageController.setCurrentLevel() \u88AB\u8C03\u7528");
        cc.log("   - \u65E7\u5173\u5361\u7F16\u53F7: " + this.currentLevel);
        cc.log("   - \u65B0\u5173\u5361\u7F16\u53F7: " + levelNumber);
        cc.log("   - \u8282\u70B9\u6FC0\u6D3B\u72B6\u6001: " + this.node.active);
        this.currentLevel = levelNumber;
        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    };
    /**
     * 获取当前关卡编号
     */
    LevelPageController.prototype.getCurrentLevel = function () {
        return this.currentLevel;
    };
    /**
     * 获取当前关卡信息
     */
    LevelPageController.prototype.getCurrentLevelInfo = function () {
        return this.currentLevelInfo;
    };
    /**
     * 隐藏所有地图容器
     */
    LevelPageController.prototype.hideAllMapContainers = function () {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
            cc.log("✅ 确保 level_page 节点激活");
        }
        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
            cc.log("隐藏 game_map_1 容器");
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
            cc.log("隐藏 game_map_2 容器");
        }
        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    };
    /**
     * 显示 game_map_1 容器（方形地图）
     */
    LevelPageController.prototype.showGameMap1 = function () {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
            cc.log("✅ 显示 game_map_1 容器");
        }
        else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    };
    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    LevelPageController.prototype.showGameMap2 = function () {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;
            cc.log("✅ 显示 game_map_2 容器");
        }
        else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    };
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "backButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], LevelPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelPageNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap1Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap2Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan10x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS001Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS002Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS003Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS004Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS005Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS006Node", void 0);
    LevelPageController = __decorate([
        ccclass
    ], LevelPageController);
    return LevelPageController;
}(cc.Component));
exports.default = LevelPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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