
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/test/NoticeRoundStartTest.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a1b2cPU5fZ4kKvN7xI0VniQ', 'NoticeRoundStartTest');
// scripts/test/NoticeRoundStartTest.ts

"use strict";
// 测试NoticeRoundStart消息处理的脚本
// 这个脚本可以用来模拟发送NoticeRoundStart消息，测试前端计时器更新功能
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var MessageId_1 = require("../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var NoticeRoundStartTest = /** @class */ (function (_super) {
    __extends(NoticeRoundStartTest, _super);
    function NoticeRoundStartTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.testButton = null;
        _this.firstChoiceTestButton = null;
        _this.statusLabel = null;
        return _this;
    }
    NoticeRoundStartTest.prototype.start = function () {
        if (this.testButton) {
            this.testButton.node.on('click', this.sendTestMessage, this);
        }
        if (this.firstChoiceTestButton) {
            this.firstChoiceTestButton.node.on('click', this.testFirstChoiceBonusFlow, this);
        }
        if (this.statusLabel) {
            this.statusLabel.string = '点击按钮测试消息';
        }
    };
    // 发送测试的NoticeRoundStart消息
    NoticeRoundStartTest.prototype.sendTestMessage = function () {
        // 创建测试数据
        var testData = {
            roundNumber: 1,
            countDown: 25,
            gameStatus: 0
        };
        // 模拟接收到的消息格式
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeRoundStart,
            code: 0,
            msg: "success",
            data: testData
        };
        // 发送消息事件
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001\u6D4B\u8BD5\u6D88\u606F: \u56DE\u5408" + testData.roundNumber + ", \u5012\u8BA1\u65F6" + testData.countDown + "\u79D2";
        }
    };
    // 发送倒计时更新测试
    NoticeRoundStartTest.prototype.sendCountdownUpdate = function (seconds) {
        var testData = {
            roundNumber: 1,
            countDown: seconds,
            gameStatus: 0
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeRoundStart,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5012\u8BA1\u65F6\u66F4\u65B0: " + seconds + "\u79D2";
        }
    };
    // 测试不同的倒计时值
    NoticeRoundStartTest.prototype.testDifferentCountdowns = function () {
        var _this = this;
        // 测试25秒倒计时
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(25);
        }, 1);
        // 测试20秒倒计时（进入展示阶段）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(20);
        }, 3);
        // 测试5秒倒计时（回合结束前）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(5);
        }, 5);
        // 测试0秒倒计时（回合结束）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(0);
        }, 7);
    };
    // 发送NoticeActionDisplay测试消息
    NoticeRoundStartTest.prototype.sendActionDisplayMessage = function () {
        var testData = {
            roundNumber: 1,
            gameStatus: 0,
            countDown: 5,
            playerActions: [
                {
                    userId: "player_001",
                    x: 3,
                    y: 2,
                    action: 1,
                    score: 1,
                    isFirstChoice: true,
                    result: 2 // 数字2
                },
                {
                    userId: "player_002",
                    x: 1,
                    y: 4,
                    action: 2,
                    score: 1,
                    isFirstChoice: false,
                    result: "correct_mark"
                }
            ],
            playerTotalScores: {
                "player_001": 5,
                "player_002": 3
            },
            remainingMines: 10,
            message: "展示阶段：显示所有玩家操作"
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeActionDisplay,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001ActionDisplay\u6D88\u606F: \u5C55\u793A\u9636\u6BB5\uFF0C\u5269\u4F59" + testData.countDown + "\u79D2\uFF0C\u5269\u4F59\u70B8\u5F39" + testData.remainingMines + "\u4E2A";
        }
    };
    // 测试完整的回合流程
    NoticeRoundStartTest.prototype.testFullRoundFlow = function () {
        var _this = this;
        // 1. 发送回合开始
        this.sendTestMessage();
        // 2. 20秒后发送操作展示
        this.scheduleOnce(function () {
            _this.sendActionDisplayMessage();
        }, 2);
        if (this.statusLabel) {
            this.statusLabel.string = '开始测试完整回合流程...';
        }
    };
    // 测试先手奖励和后续加分流程
    NoticeRoundStartTest.prototype.testFirstChoiceBonusFlow = function () {
        var _this = this;
        // 1. 发送回合开始
        this.sendTestMessage();
        // 2. 2秒后发送先手奖励
        this.scheduleOnce(function () {
            _this.sendFirstChoiceBonusMessage();
        }, 2);
        // 3. 4秒后发送操作展示（包含先手玩家）
        this.scheduleOnce(function () {
            _this.sendActionDisplayWithFirstChoiceMessage();
        }, 4);
        if (this.statusLabel) {
            this.statusLabel.string = '测试先手奖励+本回合加分...';
        }
    };
    // 发送NoticeFirstChoiceBonus测试消息
    NoticeRoundStartTest.prototype.sendFirstChoiceBonusMessage = function () {
        var testData = {
            userId: "player_001",
            roundNumber: 1,
            bonusScore: 1,
            totalScore: 6 // 原来5分 + 1分先手奖励
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeFirstChoiceBonus,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001FirstChoiceBonus: player_001\u83B7\u5F97+1\u5148\u624B\u5956\u52B1";
        }
    };
    // 发送包含先手玩家的NoticeActionDisplay测试消息
    NoticeRoundStartTest.prototype.sendActionDisplayWithFirstChoiceMessage = function () {
        console.log('发送包含先手玩家的NoticeActionDisplay消息');
        var testData = {
            roundNumber: 1,
            gameStatus: 0,
            countDown: 5,
            playerActions: [
                {
                    userId: "player_001",
                    x: 3,
                    y: 2,
                    action: 1,
                    score: 2,
                    isFirstChoice: true,
                    result: 3 // 数字3
                },
                {
                    userId: "player_002",
                    x: 1,
                    y: 4,
                    action: 2,
                    score: 1,
                    isFirstChoice: false,
                    result: "correct_mark"
                }
            ],
            playerTotalScores: {
                "player_001": 8,
                "player_002": 4 // 原来3分 + 本回合1分
            },
            remainingMines: 9,
            message: "展示阶段：先手玩家应该显示两次player_game_pfb分数变化"
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeActionDisplay,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001ActionDisplay: \u5148\u624B\u73A9\u5BB6\u5E94\u663E\u793A+2\u5206";
        }
    };
    NoticeRoundStartTest.prototype.onDestroy = function () {
        if (this.testButton) {
            this.testButton.node.off('click', this.sendTestMessage, this);
        }
        if (this.firstChoiceTestButton) {
            this.firstChoiceTestButton.node.off('click', this.testFirstChoiceBonusFlow, this);
        }
    };
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testButton", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "firstChoiceTestButton", void 0);
    __decorate([
        property(cc.Label)
    ], NoticeRoundStartTest.prototype, "statusLabel", void 0);
    NoticeRoundStartTest = __decorate([
        ccclass
    ], NoticeRoundStartTest);
    return NoticeRoundStartTest;
}(cc.Component));
exports.default = NoticeRoundStartTest;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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