"use strict";
cc._RF.push(module, '83ee5xvZ1VBLb3UezEVIqou', 'zh_CN');
// resources/i18n/zh_CN.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.language = void 0;
exports.language = {
    //这部分是通用的
    kickout1: '您被请出房间',
    LeaveRoom: '房间已解散',
    InsufficientBalance: '余额不足，去充值',
    GameRouteNotFound: '游戏线路异常',
    NetworkError: '网络异常',
    RoomIsFull: '房间已满',
    EnterRoomNumber: '输入房间号',
    GetUserInfoFailed: '获取用户信息失败',
    RoomDoesNotExist: '房间不存在',
    FailedToDeductGoldCoins: '扣除金币失败',
    ExitApplication: '确定退出游戏？',
    QuitTheGame: '退出后将无法返回游戏。',
    NotEnoughPlayers: '玩家数量不足',
    TheGameIsFullOfPlayers: '玩家数量已满',
    kickout2: '是否将 {0} 请出房间?',
    upSeat: '加入游戏',
    downSeat: '退出游戏',
    startGame: '开始',
    readyGame: '准备',
    cancelGame: '取消准备',
    cancel: '取消',
    confirm: '确定',
    kickout3: '踢出',
    back: '返回',
    leave: '退出',
    music: '音乐',
    sound: '音效',
    join: '加入',
    create: '创建',
    auto: '匹配',
    Room: '房间',
    room_number: '房间号',
    copy: '复制',
    game_amount: '游戏费用',
    player_numbers: '玩家数量:',
    room_exist: '房间不存在',
    enter_room_number: '输入房间号',
    free: '免费',
    players: '玩家',
    Player: '玩家',
    Tickets: '门票',
    Empty: '空位',
    nextlevel: '下一关',
    relevel: '再玩一次',
    //帮助
    Tips: "\u5E2E\u52A9",
    Tips1: "1. \u6BCF\u5C40\u6E38\u620F\u6BD4\u8D5B\u65F6\u95F4\uFF1A120\u79D2\u3002",
    Tips2: "2. \u6392\u540D\u89C4\u5219\uFF1A\u6E38\u620F\u65F6\u95F4\u7ED3\u675F\u540E\uFF0C\u6839\u636E\u73A9\u5BB6\u7684\u5F97\u5206\u9AD8\u4F4E\u8FDB\u884C\u6392\u540D\uFF0C\u5F97\u5206\u76F8\u540C\u6392\u540D\u76F8\u540C\u3002",
    Tips3: "3. \u6D88\u9664\u89C4\u5219\uFF1A\u901A\u8FC7\u79FB\u52A8\u6E38\u620F\u4E2D\u7684\u98DF\u7269\u4E0E\u4E34\u8FD1\u7684\u98DF\u7269\u4EA4\u6362\u4F4D\u7F6E\u4F7F\u76F8\u540C\u7684\u98DF\u7269\u5904\u4E8E\u76F8\u90BB\u72B6\u6001\uFF0C\u82E5\u6EE1\u8DB3\u76F8\u90BB\u7684\u540C\u6837\u98DF\u7269>=3\u4E2A\u4F4D\u7F6E\u4EA4\u6362\u6210\u529F\uFF0C\u5219\u5B8C\u6210\u6D88\u9664\u5F97\u5206\u3002",
    Tips4: "4. \u9053\u5177\u6D88\u9664\uFF1A\u5B8C\u6210\u7279\u6B8A\u7684\u6D88\u9664\u4F1A\u751F\u6210\u6D88\u9664\u9053\u5177\uFF0C\u9053\u5177\u548C\u9644\u8FD1\u5757\u4EA4\u6362\u4F4D\u7F6E\u540E\u4F1A\u4F7F\u7528\u5E76\u6D88\u8017\u3002",
    Tips5: "5. \u8FDE\u7EED\u6D88\u9664\uFF1A\u73A9\u5BB6\u4E00\u6B21\u6D88\u9664\u884C\u4E3A\u5F15\u53D1\u65B0\u98DF\u7269\u4EA7\u751F\uFF0C\u98DF\u7269\u4E0B\u843D\u540E\u89E6\u53D1\u7684\u6D88\u9664\u4E3A\u8FDE\u7EED\u6D88\u9664\uFF0C\u8FDE\u7EED\u6D88\u9664\u671F\u95F4\u4E0D\u53EF\u64CD\u4F5C\u3002\u4E00\u6B21\u4E0B\u843D\u671F\u95F4\u53D1\u751F\u7684\u8FDE\u7EED\u6D88\u9664\u8BB0\u4E3A1\u8F6E\u3002",
    //产生方式
    Generation_Method: "\u4EA7\u751F\u65B9\u5F0F\u548C\u4F7F\u7528\u6548\u679C",
    Generation_Method1: "1. \u6D88\u9664\u6574\u5217\u6216\u6574\u884C\u98DF\u7269\u3002",
    Generation_Method2: "2. \u6D88\u9664\u5176\u4E2D\u5FC3\u5411\u5916\u5EF6\u4F38\u768412\u4E2A\u98DF\u7269\u3002",
    Generation_Method3: "3. \u6D88\u9664\u5C4F\u5E55\u4E2D\u6240\u6709\u4E0E\u8BE5\u9053\u5177\u4EA4\u6362\u4F4D\u7F6E\u76F8\u540C\u7684\u98DF\u7269\u3002",
    Generation_Method4: "4. \u6D88\u96641\u884C+1\u5217\u7684\u98DF\u7269\uFF0C\u5305\u62EC\u9501\u94FE\u548C\u51B0\u5757\u3002",
    Generation_Method5: "5. \u6D88\u96643\u884C+3\u5217\u7684\u98DF\u7269\uFF0C\u5305\u62EC\u9501\u94FE\u548C\u51B0\u5757\u3002",
    Generation_Method6: "6. \u5C06\u5C4F\u5E55\u4E2D\u968F\u673A\u4E00\u79CD\u98DF\u7269\u53D8\u4E3A\u706B\u7BAD\u5E76\u91CA\u653E\u3002",
    Generation_Method7: "7. \u6D88\u9664\u5176\u4E2D\u5FC3\u5411\u5916\u5EF6\u4F38\u768424\u4E2A\u98DF\u7269\uFF0C\u5305\u62EC\u9501\u94FE\u548C\u51B0\u5757\u3002",
    Generation_Method8: "8. \u5C06\u5C4F\u5E55\u4E2D\u968F\u673A\u4E00\u79CD\u98DF\u7269\u5757\u53D8\u4E3A\u70B8\u5F39\u5E76\u91CA\u653E\u3002",
    Generation_Method9: "9. \u6D88\u9664\u5C4F\u5E55\u4E2D\u6240\u6709\u98DF\u7269\uFF0C\u5305\u62EC\u9501\u94FE\u548C\u51B0\u5757\u3002",
    //常驻任务
    Permanent_Task: "\u5E38\u9A7B\u4EFB\u52A1",
    Permanent_Task1: "\u73A9\u5BB6\u6BCF\u6D88\u966410\u53EA\u8783\u87F9\uFF0C\u4FBF\u4F1A\u83B7\u5F97\u4E24\u6B21\u968F\u673A\u6D88\u9664\u4E00\u57572x2\u533A\u57DF\u7684\u5956\u52B1\uFF0C\u82E5\u672C\u6B21\u6D88\u9664\u8783\u87F9\u6EE1\u8DB3\u4E24\u6B21\u53CA\u4EE5\u4E0A\uFF0C\u5219\u7B2C\u4E8C\u6B21\u5F00\u59CB\u53EA\u7ED9\u4E00\u7EC42x2\u6D88\u9664\u3002",
    //随机任务
    Random_Task: "\u968F\u673A\u4EFB\u52A1",
    Random_Task1: "\u6839\u636E\u4EBA\u6570\u589E\u52A0\u6240\u9700\u6D88\u9664\u7684\u6570\u91CF\uFF0C\u6BD4\u4F8B\u4E3A\u52A0\u4E00\u4EBA\u5219\u9700\u591A\u6D88\u9664\u4E00\u4E2A\uFF0C\u4F8B\u59822\u4EBA\u5C40\u9700\u6D88\u96647\u6B21\uFF0C3\u4EBA\u5C40\u9700\u6D88\u96648\u6B21\u3002",
    Random_Task2: "- \u6BCF\u6B21\u6700\u591A\u540C\u65F6\u5B58\u57281\u4E2A\u4EFB\u52A1\u3002",
    Random_Task3: "- \u4EFB\u52A1\u987A\u5E8F\u6BCF\u5C40\u5F00\u5C40\u968F\u673A\u751F\u6210\u4E00\u79CD\uFF0C\u4F9D\u6B21\u4EA4\u66FF\u8FDB\u884C\u3002",
    Random_Task4: "- \u6D88\u96647\u4E2A\u6307\u5B9A\u989C\u8272\u53EF\u83B7\u5F97\u4E00\u4E2A\u968F\u673A\u5F69\u3002",
    Random_Task5: "- \u6D88\u96647\u4E2A\u6307\u5B9A\u989C\u8272\u53EF\u83B7\u5F97\u4E00\u4E2A\u51B0\u5757\u3002",
    Random_Task6: "- \u6D88\u96647\u4E2A\u6307\u5B9A\u989C\u8272\u53EF\u83B7\u5F97\u4E00\u4E2A\u9501\u94FE\u3002",
    Random_Task7: "- \u6D88\u96647\u4E2A\u6307\u5B9A\u989C\u8272\u53EF\u83B7\u5F97\u4E00\u4E2A\u968F\u673A\u5F62\u6001\u7684\u706B\u7BAD\u9053\u5177\u3002",
    //锁链简介
    Chains: "\u9501\u94FE",
    Chains1: "1. \u6D88\u9664\u4EFB\u52A1\uFF1A\u6E38\u620F\u6BD4\u8D5B\u5F00\u59CB\u540E\uFF0C\u6BCF\u4F4D\u73A9\u5BB6\u4F1A\u6536\u5230\u7CFB\u7EDF\u6D3E\u53D1\u7684\u4EFB\u52A1\uFF0C\u5B8C\u6210\u4EFB\u52A1\u4F1A\u5411\u5176\u4ED6\u73A9\u5BB6\u91CA\u653E\u59A8\u788D\u9053\u5177\u2014\u2014\u9501\u94FE\u3002",
    Chains2: "2. \u9501\u94FE\u7684\u59A8\u788D\u6548\u679C\uFF1A\u968F\u673A\u9501\u4F4F\u4E00\u4E2A\u4F4D\u7F6E\u5185\u7684\u98DF\u7269\u3002\u53D7\u9650\u5236\u671F\u95F4\uFF0C\u9501\u94FE\u9501\u4F4F\u7684\u4F4D\u7F6E\u4E0D\u80FD\u79FB\u52A8\uFF0C\u9501\u94FE\u5185\u7684\u98DF\u7269\u53EF\u80FD\u4F1A\u56E0\u4E3A\u5176\u4ED6\u5730\u5757\u7684\u6D88\u9664\u800C\u6539\u53D8\uFF0C\u5E76\u975E\u9501\u4F4F\u56FA\u5B9A\u98DF\u7269\u3002",
    Chains3: "3. \u9501\u94FE\u7684\u89E3\u9664\uFF1A\u5728\u9501\u94FE\u90BB\u8FD1\u4F4D\u7F6E\uFF08\u4E0A\u4E0B\u5DE6\u53F31\u683C\u7684\u5757\uFF09\u8FDB\u884C\u666E\u901A\u6D88\u9664\u540E\u6216\u8005\u5728\u9053\u5177\u6709\u6548\u533A\u57DF\u5185\uFF0C\u6D88\u96641\u6B21\u5373\u53EF\u89E3\u9664\u3002",
    //冰块简介
    Ice_Blocks: "\u51B0\u5757",
    Ice_Blocks1: "1. \u6D88\u9664\u4EFB\u52A1\uFF1A\u6E38\u620F\u6BD4\u8D5B\u5F00\u59CB\u540E\uFF0C\u6BCF\u4F4D\u73A9\u5BB6\u4F1A\u6536\u5230\u7CFB\u7EDF\u6D3E\u53D1\u7684\u4EFB\u52A1\uFF0C\u5B8C\u6210\u4EFB\u52A1\u4F1A\u5411\u5176\u4ED6\u73A9\u5BB6\u91CA\u653E\u59A8\u788D\u9053\u5177\u2014\u2014\u51B0\u5757\u3002",
    Ice_Blocks2: "2. \u51B0\u5757\u7684\u59A8\u788D\u6548\u679C\uFF1A\u968F\u673A\u51BB\u4F4F\u4E00\u4E2A\u98DF\u7269\uFF0C\u53D7\u51BB\u671F\u95F4\uFF0C\u4F4D\u7F6E\u53EF\u4EE5\u88AB\u52A8\u79FB\u52A8\uFF0C\u73A9\u5BB6\u4E0D\u80FD\u4E3B\u52A8\u79FB\u52A8\u3002",
    Ice_Blocks3: "3. \u51B0\u5757\u7684\u89E3\u9664\uFF1A\u5728\u51B0\u5757\u90BB\u8FD1\u4F4D\u7F6E\uFF08\u4E0A\u4E0B\u5DE6\u53F31\u683C\u7684\u5757\uFF09\u8FDB\u884C\u666E\u901A\u6D88\u9664\u540E\u6216\u8005\u5728\u9053\u5177\u6709\u6548\u533A\u57DF\u5185,\u6D88\u96641\u6B21\u5373\u53EF\u89E3\u9664\u3002",
    //得分细则
    Scoring_Details: "\u5F97\u5206\u7EC6\u5219",
    Scoring_Details1: "\u666E\u901A\u6D88\u9664\uFF1A\u6D88\u9664\u5355\u5757\u5F97\u5206=20 \u5206\uFF1B",
    Scoring_Details2: "\u8FDE\u7EED\u6D88\u9664\uFF1A\u6D88\u9664\u5355\u5757\u5F97\u5206=\u8FDE\u6D88\u6B21\u6570x20 \u5206;",
    Scoring_Details3: "\u706B\u7BAD\u6D88\u9664\uFF1A\u706B\u7BAD\u6FC0\u6D3B\u5F97\u5206=300\u5206;\u7531\u706B\u7BAD\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=30\u5206;",
    Scoring_Details4: "\u70B8\u5F39\u6D88\u9664\uFF1A\u70B8\u5F39\u6FC0\u6D3B\u5F97\u5206=500\u5206;\u7531\u70B8\u5F39\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=50\u5206;",
    Scoring_Details5: "\u5F69\u8679\u6D88\u9664\uFF1A\u5F69\u8679\u6FC0\u6D3B\u5F97\u5206=600\u5206;\u7531\u5F69\u8679\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=60\u5206;",
    Scoring_Details6: "\u706B\u7BAD\u7EC4\u5408\u6D88\u9664\uFF1A\u53CC\u706B\u7BAD\u6FC0\u6D3B\u5F97\u5206=1200\u5206;\u7531\u8D85\u7EA7\u706B\u7BAD\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=60\u5206;",
    Scoring_Details7: "\u70B8\u5F39\u706B\u7BAD\u7EC4\u5408\u6D88\u9664\uFF1A\u70B8\u5F39\u706B\u7BAD\u7EC4\u5408\u6FC0\u6D3B\u5F97\u5206=1600\u5206\u7531\u70B8\u5F39\u706B\u7BAD\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=80\u5206;",
    Scoring_Details8: "\u5F69\u8679\u706B\u7BAD\u7EC4\u5408\u6D88\u9664\uFF1A\u5F69\u8679\u706B\u7BAD\u7EC4\u5408\u6FC0\u6D3B\u5F97\u5206=1800\u5206\u7531\u5F69\u8679\u706B\u7BAD\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=90\u5206;",
    Scoring_Details9: "\u70B8\u5F39\u7EC4\u5408\u6D88\u9664\uFF1A\u8D85\u7EA7\u70B8\u5F39\u6FC0\u6D3B\u5F97\u5206=2000\u5206;\u7531\u8D85\u7EA7\u70B8\u5F39\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=100\u5206;",
    Scoring_Details10: "\u5F69\u8679\u70B8\u5F39\u6D88\u9664\uFF1A\u5F69\u8679\u70B8\u5F39\u6FC0\u6D3B\u5F97\u5206=2200\u5206;\u7531\u5F69\u8679\u70B8\u5F39\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=110\u5206;",
    Scoring_Details11: "\u5F69\u8679\u7EC4\u5408\u6D88\u9664\uFF1A\u8D85\u7EA7\u5F69\u8679\u6FC0\u6D3B\u5F97\u5206=2400\u5206;\u7531\u8D85\u7EA7\u5F69\u8679\u6D88\u9664\u7684\u5355\u5757\u5F97\u5206=120\u5206;",
};
var cocos = cc;
if (!cocos.Jou_i18n)
    cocos.Jou_i18n = {};
cocos.Jou_i18n.zh_CN = exports.language;

cc._RF.pop();