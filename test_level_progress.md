# 关卡进度测试说明

## 修改总结

### 1. 关卡总数更新为30关
- `LevelSelectController.ts`: totalLevels = 30
- `LevelSelectDemo.ts`: totalLevels = 30  
- `LevelSelectDemoInLevel.ts`: totalLevels = 30
- `LevelSelectExample.ts`: totalLevels = 30

### 2. 后端数据格式
后端应该返回如下格式的数据：
```json
{
  "clearedLevels": 0,
  "currentLevel": 1, 
  "totalLevels": 30
}
```

### 3. 关卡状态逻辑
- **绿色（已完成）**: 关卡号 < currentLevel 的所有关卡
- **黄色（当前可玩）**: 关卡号 = currentLevel 的关卡
- **灰色（未解锁）**: 关卡号 > currentLevel 的所有关卡

### 4. 数据流程
1. 登录成功后发送 `ExtendLevelProgress` 请求
2. 后端返回关卡进度数据
3. `GlobalManagerController` 接收消息并调用 `hallPageController.setLevelProgress(levelProgressData)`
4. 数据传递链：`HallPageController` → `LevelSelectPageController` → `LevelSelectController`
5. `LevelSelectController.setLevelProgress()` 根据 `currentLevel` 设置关卡状态
6. 界面自动滚动到 `currentLevel` 并选中

### 5. 测试场景示例
- `{"clearedLevels": 0, "currentLevel": 1, "totalLevels": 30}` - 第1关可玩，其他锁定
- `{"clearedLevels": 5, "currentLevel": 6, "totalLevels": 30}` - 前5关绿色，第6关黄色，其他灰色
- `{"clearedLevels": 29, "currentLevel": 30, "totalLevels": 30}` - 前29关绿色，第30关黄色（特殊关卡）
- `{"clearedLevels": 30, "currentLevel": 30, "totalLevels": 30}` - 全部关卡完成，第30关仍显示为当前关卡

### 6. 兼容性
- 保留了 `setLevelProgressLegacy()` 方法以兼容旧接口
- 如果后端传了 `totalLevels`，会动态更新关卡总数
