# 关卡页面控制器使用说明

## 概述

已成功创建关卡页面控制器 `LevelPageController`，实现了以下功能：

1. 点击开始游戏按钮发送 `ExtendLevelInfo` 消息到后端获取地图数据
2. 更新地雷数的UI显示
3. 根据关卡数进入相应的关卡，并打开相应的节点，隐藏别的地图节点

## 文件结构

### 新增文件
- `assets/scripts/level/LevelPageController.ts` - 关卡页面控制器

### 修改文件
- `assets/scripts/net/MessageId.ts` - 添加了 `MsgTypeExtendLevelInfo` 消息ID
- `assets/scripts/bean/GameBean.ts` - 添加了关卡信息相关的数据结构
- `assets/scripts/GlobalManagerController.ts` - 添加了关卡页面支持和消息处理
- `assets/scripts/hall/Level/LevelSelectPageController.ts` - 修改了进入关卡的逻辑

## 关卡地图节点映射规则

根据需求，关卡与地图节点的对应关系如下：

### 方形地图 (game_map_1)
- **第1-4关**: `level_page/game_map_1/chess_bg/qipan8*8`
- **第6-9关**: `level_page/game_map_1/chess_bg/qipan8*9`
- **第11-14关**: `level_page/game_map_1/chess_bg/qipan9*9`
- **第16-19关**: `level_page/game_map_1/chess_bg/qipan9*10`
- **第21-24关**: `level_page/game_map_1/chess_bg/qipan10*10`
- **第26-29关**: `level_page/game_map_1/chess_bg/qipan10*10`

### 特殊关卡 (game_map_2)
- **第5关**: `level_page/game_map_2/game_bg/Level_S001`
- **第10关**: `level_page/game_map_2/game_bg/Level_S002`
- **第15关**: `level_page/game_map_2/game_bg/Level_S003`
- **第20关**: `level_page/game_map_2/game_bg/Level_S004`
- **第25关**: `level_page/game_map_2/game_bg/Level_S005`
- **第30关**: `level_page/game_map_2/game_bg/Level_S006`

## 使用流程

1. **关卡选择**: 在关卡选择页面选择关卡并点击"开始游戏"按钮
2. **页面跳转**: 自动跳转到关卡页面 (`PageType.LEVEL_PAGE`)
3. **发送请求**: 点击关卡页面的"开始游戏"按钮，发送 `ExtendLevelInfo` 消息到后端
4. **接收响应**: 后端返回关卡信息，包含地图类型、地雷数等数据
5. **UI更新**: 更新地雷数UI显示
6. **地图切换**: 根据关卡编号显示对应的地图节点，隐藏其他节点

## 数据结构

### ExtendLevelInfoRequest (请求)
```typescript
interface ExtendLevelInfoRequest {
    levelNumber: number; // 关卡编号
}
```

### ExtendLevelInfoResponse (响应)
```typescript
interface ExtendLevelInfoResponse {
    levelNumber: number; // 关卡编号
    mapType: number; // 地图类型 0-方形地图，1-六边形地图
    mapConfig?: MapConfig; // 地图配置信息（方形地图）
    validHexCoords?: HexCoord[]; // 有效的六边形坐标列表（六边形地图）
    mineCount: number; // 地雷总数
    mapSize: string; // 地图大小描述
}
```

## 主要方法

### LevelPageController 主要方法

- `setCurrentLevel(levelNumber: number)` - 设置当前关卡
- `onExtendLevelInfo(levelInfo: ExtendLevelInfoResponse)` - 处理关卡信息响应
- `updateMineCountUI(mineCount: number)` - 更新地雷数UI
- `enterLevel(levelNumber: number)` - 根据关卡数进入相应关卡
- `hideAllMapNodes()` - 隐藏所有地图节点

## 配置要求

在Cocos Creator编辑器中，需要为 `LevelPageController` 组件配置以下属性：

### 必需属性
- `startGameButton` - 开始游戏按钮
- `mineCountLabel` - 地雷数显示标签
- `levelPageNode` - level_page节点
- `gameMap1Node` - game_map_1节点
- `gameMap2Node` - game_map_2节点

### 地图节点属性
- `qipan8x8Node` - 8x8棋盘节点
- `qipan8x9Node` - 8x9棋盘节点
- `qipan9x9Node` - 9x9棋盘节点
- `qipan9x10Node` - 9x10棋盘节点
- `qipan10x10Node` - 10x10棋盘节点
- `levelS001Node` - 特殊关卡S001节点
- `levelS002Node` - 特殊关卡S002节点
- `levelS003Node` - 特殊关卡S003节点
- `levelS004Node` - 特殊关卡S004节点
- `levelS005Node` - 特殊关卡S005节点
- `levelS006Node` - 特殊关卡S006节点

## 注意事项

1. 确保所有地图节点都正确配置到 `LevelPageController` 组件上
2. 后端需要实现 `ExtendLevelInfo` 消息的处理逻辑
3. 地图节点的路径必须与代码中的映射规则一致
4. 确保 `GlobalManager` 节点上挂载了 `GlobalManagerController` 组件
5. 关卡页面需要在场景中正确设置并挂载 `LevelPageController` 组件

## 扩展说明

如果需要添加更多关卡或修改地图节点映射规则，只需要修改 `LevelPageController.ts` 中的 `enterLevel` 方法即可。
