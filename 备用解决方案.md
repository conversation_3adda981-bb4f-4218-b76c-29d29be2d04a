# 备用解决方案

## 当前状态
- ✅ 成功找到了 `Canvas/global_node` 节点
- ❓ 需要确认是否能成功获取 `GlobalManagerController` 组件

## 测试步骤
请再次点击"开始游戏"按钮，查看控制台输出：

### 期望的成功输出：
```
进入关卡 1
❌ 未找到 global_node 节点（根目录）
✅ 找到 Canvas/global_node 节点
✅ 成功获取 GlobalManagerController 组件
```

### 如果失败，会看到：
```
进入关卡 1
❌ 未找到 global_node 节点（根目录）
✅ 找到 Canvas/global_node 节点
❌ Canvas/global_node 节点上没有 GlobalManagerController 组件
节点上的组件列表: ["Transform", "SomeOtherComponent", ...]
```

## 备用方案1：事件系统

如果组件获取仍然有问题，我们可以使用事件系统：

### 步骤1：在 EventCenter.ts 中添加事件
```typescript
export enum EventType {
    // ... 现有事件
    SwitchToLevelPage = 'switchToLevelPage', // 切换到关卡页面
}
```

### 步骤2：在 GlobalManagerController 中监听事件
```typescript
onLoad() {
    // ... 现有代码
    GameMgr.Event.AddEventListener(EventType.SwitchToLevelPage, this.onSwitchToLevelPage, this);
}

private onSwitchToLevelPage(selectedLevel: number) {
    if (this.levelPageController) {
        this.levelPageController.setCurrentLevel(selectedLevel);
    }
    this.setCurrentPage(PageType.LEVEL_PAGE);
}
```

### 步骤3：在 LevelSelectPageController 中发送事件
```typescript
public enterSelectedLevel() {
    if (this.levelSelectController) {
        const selectedLevel = this.levelSelectController.getCurrentSelectedLevel();
        cc.log(`进入关卡 ${selectedLevel}`);
        
        // 使用事件系统通知全局管理器
        GameMgr.Event.Send(EventType.SwitchToLevelPage, selectedLevel);
    }
}
```

## 备用方案2：单例模式

让 GlobalManagerController 使用单例模式：

### 在 GlobalManagerController.ts 中添加：
```typescript
export default class GlobalManagerController extends cc.Component {
    private static instance: GlobalManagerController = null;

    onLoad() {
        GlobalManagerController.instance = this;
        // ... 现有代码
    }

    public static getInstance(): GlobalManagerController {
        return GlobalManagerController.instance;
    }

    onDestroy() {
        GlobalManagerController.instance = null;
        // ... 现有代码
    }
}
```

### 在 LevelSelectPageController 中使用：
```typescript
public enterSelectedLevel() {
    if (this.levelSelectController) {
        const selectedLevel = this.levelSelectController.getCurrentSelectedLevel();
        cc.log(`进入关卡 ${selectedLevel}`);
        
        const globalManager = GlobalManagerController.getInstance();
        if (globalManager) {
            if (globalManager.levelPageController) {
                globalManager.levelPageController.setCurrentLevel(selectedLevel);
            }
            globalManager.setCurrentPage(PageType.LEVEL_PAGE);
        } else {
            cc.error("GlobalManagerController 单例未初始化");
        }
    }
}
```

## 推荐方案

我推荐使用**事件系统**（备用方案1），因为：
1. 解耦性更好
2. 不依赖节点查找
3. 更符合现有的架构模式
4. 更容易维护和扩展

请先测试当前的调试代码，然后告诉我结果。如果仍然有问题，我们就实施事件系统方案。
