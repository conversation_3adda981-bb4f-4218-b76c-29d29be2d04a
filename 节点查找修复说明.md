# 节点查找修复说明

## 问题描述
```
LevelSelectPageController.ts:79 Uncaught TypeError: Cannot read properties of null (reading 'getComponent')
```

## 修复内容

### 1. 多重节点查找策略
现在代码会按以下顺序尝试查找 GlobalManagerController：

1. **查找 "global_node" 节点**（基于用户提示）
2. **查找 "GlobalManager" 节点**（原始名称）
3. **查找 "Canvas" 节点**（常见的根节点）
4. **遍历所有场景根节点**（最后的兜底方案）

### 2. 调试信息
如果所有方法都失败，代码会：
- 输出错误信息
- 列出场景中所有根节点的名称，帮助调试

## 测试步骤

1. **点击开始游戏按钮**
2. **检查控制台输出**：
   - 如果成功：应该看到 `进入关卡 X` 和页面切换
   - 如果失败：会看到错误信息和场景节点列表

## 可能的节点名称

根据代码，会尝试查找以下节点：
- `global_node` ✅ (用户提示的名称)
- `GlobalManager` (原始代码中的名称)
- `Canvas` (常见的根节点名称)

## 如果问题仍然存在

### 检查场景配置
1. 打开场景文件
2. 确认有节点挂载了 `GlobalManagerController` 组件
3. 记录该节点的确切名称

### 手动指定节点名称
如果节点名称不是上述任何一个，可以直接修改代码：

```typescript
// 将 "your_actual_node_name" 替换为实际的节点名称
let globalManagerNode = cc.find("your_actual_node_name");
```

### 查看调试输出
运行后查看控制台输出的节点列表，找到正确的节点名称。

## 预期结果

修复后，点击"开始游戏"按钮应该：
1. 不再出现 `Cannot read properties of null` 错误
2. 成功找到 GlobalManagerController
3. 正确设置关卡编号
4. 成功切换到关卡页面

## 备用方案

如果仍然有问题，可以考虑：
1. 使用单例模式管理 GlobalManagerController
2. 使用事件系统进行页面切换
3. 在场景加载时缓存 GlobalManagerController 引用
